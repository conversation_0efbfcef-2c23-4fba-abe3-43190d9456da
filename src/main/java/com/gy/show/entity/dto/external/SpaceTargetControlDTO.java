package com.gy.show.entity.dto.external;

import lombok.Data;

import java.util.List;

/**
 * 航天站过程控制 消息体
 */
@Data
public class SpaceTargetControlDTO {

    /**
     *  1\2
     */
    private int mainSwitch;

    private List<TargetControlCommand> commands;

    private String stationId;

    @Data
    public static class TargetControlCommand {

        /**
         * 目标序号
         */
        private int seq;

        /**
         * 遥控 \ 遥测
         */
        private int isOpen;

    }
}


