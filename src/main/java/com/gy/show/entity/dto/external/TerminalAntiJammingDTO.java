package com.gy.show.entity.dto.external;

import lombok.Data;

@Data
public class TerminalAntiJammingDTO {

    /**
     * 节点序号 0-5
     */
    private Integer targetNode;

    /**
     * 决策方式
     * 0：无效
     * 1：手动
     * 2：自动
     */
    private Integer decisionMethod;

    /**
     * 抗干扰手段
     * 0：关闭抗干扰
     * 1：（不显示）
     * 2：体制级抗干扰
     * 3：（不显示）
     * 4：信号级抗干扰
     * 5：系统级抗干扰
     * 6：自主决策
     */
    private Integer counterInterferenceMeasures;

    /**
     * 链路切换方式
     * 0：无效
     * 1：系统级抗干扰
     * 2：切换调度
     * 3：接入调度
     */
    private Integer switchLink;

    /**
     * 链路切换代号
     * 0：无效
     * 1~9：代表选用链路1~9（后期再与终端确定）
     */
    private Integer switchLinkCode;

}
