package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR> 目标数据表
 */
@Data
public class DataEquipmentOccupancyDTO extends ObjectConvert<DataEquipmentOccupancy> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 主表ID
     */
    private String generalId;

    /**
     * 资源域ID
     */
    private String areaId;

    /**
     * 调度结果 1 已受理 2 已拒绝 3 校验未通过
     */
    private Integer scheduleState;

    private String areaName;

    private Integer dataType;

    /**
     * 具体类型，如固定站等
     */
    private String type;

    /**
     * 设备模型ID
     */
    private String equipmentId;

    private Map<String, Object> equipmentDetail;

    /**
     * 是否激活
     */
    private Byte isActive;

    private BigDecimal coverRate;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    private static final long serialVersionUID = 1L;
}