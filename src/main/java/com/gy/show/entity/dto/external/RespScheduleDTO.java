package com.gy.show.entity.dto.external;

import lombok.Data;

@Data
public class RespScheduleDTO {

    /**
     * 接口类型
     */
    private byte interfaceType;

    /**
     * 任务数量
     */
    private short taskNum;

    /**
     * 任务序号
     */
    private short taskSeq;

    /**
     * 需求ID
     */
    private Long requirementId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 目标ID
     */
    private Long targetId;

    /**
     * 节点ID
     */
    private Long equipmentId;

    /**
     * 业务类型
     */
    private byte bzTpe;

    /**
     * 接收情况
     */
    private byte state;
}
