package com.gy.show.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Data
public class AlgorithmDTO {

    /**
     * 需求ID
     */
//    private String requirementId;

    @NotEmpty(message = "请先选择需求进行调度的任务")
    private List<String> taskIds;

    private String areaId;

    @ApiModelProperty("1 覆盖率排序算法 2 优先级排序算法 3 三方调度算法")
    @NotNull(message = "请选择对应算法")
    private Integer algo;

    /**
     * 优先级列表,主要存储为dataType
     */
    private LinkedList<Integer> priorityList;

    /**
     * 设备列表，主要用于手动调度选择设备后需要计算进出时间使用
     */
    private List<Map<String, Object>> equipments;

}

