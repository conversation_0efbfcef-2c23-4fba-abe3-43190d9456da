package com.gy.show.entity.dto.external;

import lombok.Data;

@Data
public class TerminalMachineDTO {

    /**
     * 节点序号 0-5
     */
    private Integer targetNode;

    /**
     * 节点ID
     */
//    private String nodeId;

    /**
     * 体制选择
     * 0：不切换体制
     * 1：航天测控体制
     * 2：无人机测控体制
     * 3：DD测控体制
     * 4：频谱感知
     */
    private Integer modeCtrl = 0;

    /**
     * 接收频率 1750-2120/29000-31000MHz
     */
    private Integer rxFreq = 0;

    /**
     * 发射频率 2200-2400/18200-21200MHz
     */
    private Integer txFreq = 0;

    /**
     * 发射衰减 0-30dB
     */
    private Integer txAtte = 0;
}
