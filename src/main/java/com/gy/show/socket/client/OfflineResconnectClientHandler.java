//package com.gy.show.socket.client;
//
//import io.netty.channel.ChannelHandlerContext;
//import io.netty.channel.ChannelInboundHandlerAdapter;
//import io.netty.channel.EventLoop;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.concurrent.TimeUnit;
//
//@Slf4j
//@Component
//public class OfflineResconnectClientHandler extends ChannelInboundHandlerAdapter {
//
////    private static String mainNodeIp;
////    @Value("${node.mainNodeIp}")
////    public  void setMainNodeIp(String mainNodeIp) {
////        OfflineResconnectClientHandler.mainNodeIp = mainNodeIp;
////    }
////
////    private static int mainNodePort;
////    @Value("${node.mainNodePort}")
////    public  void setMainNodePort(int mainNodePort) {
////        OfflineResconnectClientHandler.mainNodePort = mainNodePort;
////    }
//
//    private TcpProtocolClient nettyClient;
//
//    private static int retries = 0;
//    private RetryPolicy retryPolicy;
//
//    public OfflineResconnectClientHandler(TcpProtocolClient nettyClient) {
//        this.nettyClient = nettyClient;
//    }
//
//    public OfflineResconnectClientHandler setRetryPolicy(RetryPolicy retryPolicy) {
//        this.retryPolicy = retryPolicy;
//        return this;
//    }
//    // ....
//
//    /**
//     * <AUTHOR>
//     * <p> 运行时断线重连</p>
//     * @Param [ctx]
//     * @Return
//     */
//    @Override
//    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
//        final EventLoop eventLoop = ctx.channel().eventLoop();
//
////        eventLoop.schedule(new Runnable() {
////            @Override
////            public void run() {
////                log.info("netty重连");
////                nettyClient.connect(mainNodeIp,mainNodePort );
////            }
////        }, 1L, TimeUnit.SECONDS);
//
//        boolean allowRetry = retryPolicy.allowRetry(retries);
//        if (allowRetry){
//            long sleepTimeMs = retryPolicy.getSleepTimeMs(retries);
//            log.info("netty[运行时]尝试重连次数:{} 等待时间:{}",++retries,sleepTimeMs);
//            eventLoop.schedule(() -> {
////                nettyClient.connect(mainNodeIp,mainNodePort);
//            }, sleepTimeMs, TimeUnit.MILLISECONDS);
//        }
//        super.channelInactive(ctx);
//    }
//
//    // ....
//}
