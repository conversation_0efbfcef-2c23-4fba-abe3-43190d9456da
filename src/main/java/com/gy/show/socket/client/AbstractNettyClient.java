package com.gy.show.socket.client;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.InternetProtocolFamily;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.AttributeKey;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

public abstract class AbstractNettyClient {

    private static final Logger log = LoggerFactory.getLogger(AbstractNettyClient.class);

    protected Channel channel;

    private String id;

    //配置服务端NIO线程组
    protected EventLoopGroup workerGroup = new NioEventLoopGroup();

    protected ChannelFuture channelFuture;

//    public ChannelFuture connect(String host, int port, SocketProtocolEnum protocol) {
//        switch (protocol) {
//            case TCP:
//                connect(host, port, NioSocketChannel.class);
//                break;
//            case UDP:
//                connect(host, port, NioDatagramChannel.class);
//                break;
//            default:
//                break;
//        }
//
//        return channelFuture;
//    }

    public AbstractNettyClient() {
    }

    public AbstractNettyClient(String id) {
        this.id = id;
    }

    public ChannelFuture connect(String ip, int port) {
        ChannelFuture future = null;
        try {
            Bootstrap b = new Bootstrap();
            b.group(workerGroup);
            b.option(ChannelOption.SO_REUSEADDR, true);
            b.option(ChannelOption.SO_BROADCAST, true);
            b.option(ChannelOption.SO_RCVBUF, 4096 * 1024);
            b.channel(NioDatagramChannel.class);
            b.handler(createHandler());

            future = b.bind(port).sync();
            NioDatagramChannel datagramChannel = (NioDatagramChannel) future.channel();

            datagramChannel.closeFuture().addListener(f -> destroy());
        } catch (Exception e) {
            log.error("连接UDP失败", e);
            destroy();
        }
        return future;
    }

    public ChannelFuture joinMultiGroup(String ip, int port, String localIp) {
        // 组播地址
        InetSocketAddress groupAddress = new InetSocketAddress(ip, port);

        ChannelFuture future = null;
        try {
            NetworkInterface ni = NetworkInterface.getByInetAddress(InetAddress.getByName(localIp));
            Enumeration<InetAddress> addresses = ni.getInetAddresses();

            InetAddress localAddress = null;
            while (addresses.hasMoreElements()) {
                InetAddress address = addresses.nextElement();
                if (address instanceof Inet4Address) {
                    localAddress = address;
                    log.info("网络接口名称为：{}", ni.getName());
                    log.info("网卡接口地址：{}", address.getHostAddress());
                }
            }

            Bootstrap b = new Bootstrap();
            b.group(workerGroup);
            b.channelFactory((ChannelFactory<NioDatagramChannel>) () -> new NioDatagramChannel(InternetProtocolFamily.IPv4));
            b.localAddress(new InetSocketAddress(localAddress, port));
            b.handler(createHandler());
            b.option(ChannelOption.IP_MULTICAST_IF, ni);
            b.option(ChannelOption.IP_MULTICAST_ADDR, InetAddress.getByName(localIp));
            b.option(ChannelOption.SO_REUSEADDR, true);
//            b.option(ChannelOption.SO_RCVBUF, 4096 * 1024);
//            b.option(ChannelOption.SO_SNDBUF, 4096 * 1024);
            b.option(ChannelOption.RCVBUF_ALLOCATOR, new FixedRecvByteBufAllocator(4096 * 1024));

            future = b.bind(groupAddress.getPort()).sync();
            NioDatagramChannel datagramChannel = (NioDatagramChannel) future.channel();

            datagramChannel.joinGroup(groupAddress, ni).sync();

            if (StringUtils.isNotBlank(id)) {
                // 为每个channel设置id，以便后续进行区分
                datagramChannel.attr(AttributeKey.valueOf("id")).set(id);
            }
            datagramChannel.closeFuture().addListener(f -> destroy());
        } catch (Exception e) {
            log.error("加入UDP组播失败", e);
            destroy();
        }
        return future;
    }

    public void destroy() {
        log.info("Netty客户端连接关闭");
        if (channel != null && channel.isOpen()) {
            workerGroup.shutdownGracefully();
        }
    }

    public ChannelFuture getChannelFuture() {
        return channelFuture;
    }

    protected abstract ChannelInitializer<NioDatagramChannel> createHandler();

}
