package com.gy.show.socket.client;

import lombok.extern.slf4j.Slf4j;

import java.util.Random;

@Slf4j
public class ExponentialBackOffRetry implements RetryPolicy {

    /**
     * 最大可以重连的次数
     */
    private static final int MAX_RETRY_LIMIT = 30;
    /**
     * 默认重连最长的等待时间
     */
    private static final int DEFAULT_MAX_SLEEP_MS = Integer.MAX_VALUE;
    private final Random random = new Random();
    private final int baseSleepTimesMs;
    /**
     * 最大重连时间
     */
    private final int maxRetries;
    /**
     * 最大休眠时间
     */
    private final int maxSleepMs;

    public ExponentialBackOffRetry(int baseSleepTimesMs,int maxRetries) {
        this(baseSleepTimesMs,maxRetries,DEFAULT_MAX_SLEEP_MS);
    }

    public ExponentialBackOffRetry(int baseSleepTimesMs, int maxRetries, int maxSleepMs) {
        this.maxRetries = maxRetries;
        this.baseSleepTimesMs = baseSleepTimesMs;
        this.maxSleepMs = maxSleepMs;
    }

    @Override
    public boolean allowRetry(int retryCount) {
        return retryCount < maxRetries;
    }

    @Override
    public long getSleepTimeMs(int retryCount) {
        if (retryCount < 0){
            throw new IllegalArgumentException("重连次数必须大于0");
        }
        if (retryCount > MAX_RETRY_LIMIT){
            log.error("重连次数已经到达上限");
            retryCount = MAX_RETRY_LIMIT;
        }
        long sleepMs = (long) baseSleepTimesMs * Math.max(1,random.nextInt(1 << retryCount));
        if (sleepMs > maxSleepMs){
            log.info("睡眠时间太长");
            sleepMs = maxSleepMs;
        }
        return sleepMs;
    }
}
