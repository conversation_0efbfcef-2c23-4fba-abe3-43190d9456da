package com.gy.show.socket.client;

import com.gy.show.socket.BaseChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.springframework.stereotype.Component;

@Component
public class TcpChannelInitializer extends BaseChannelInitializer {

    @Override
    protected void initChannel(SocketChannel channel) throws Exception {
        super.initChannel(channel);
        // 在管道中添加我们自己的接收数据实现方法
//        channel.pipeline().addLast(new FileTransferClientHandler());

    }

}
