package com.gy.show.socket.client;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.nio.NioDatagramChannel;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Uav1StationUdpClient extends AbstractNettyClient {

    public Uav1StationUdpClient(String id) {
        super(id);
    }

    protected ChannelInitializer<NioDatagramChannel> createHandler() {
        return new UavStationUdpChannelInitializer();
    }
}
