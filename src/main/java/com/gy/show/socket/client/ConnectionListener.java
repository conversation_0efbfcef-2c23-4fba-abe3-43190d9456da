//package com.gy.show.socket.client;
//
//import io.netty.channel.ChannelFuture;
//import io.netty.channel.ChannelFutureListener;
//import io.netty.channel.ChannelHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@ChannelHandler.Sharable
//@Component
//public class ConnectionListener implements ChannelFutureListener {
//
//    private AbstractNettyClient abstractNettyClient;
//    private static int retries = 0;
//    private RetryPolicy retryPolicy;
//
//    public ConnectionListener setRetryPolicy(RetryPolicy retryPolicy) {
//        this.retryPolicy = retryPolicy;
//        return this;
//    }
//
//    public ConnectionListener(TcpProtocolClient abstractNettyClient) {
//        this.abstractNettyClient = abstractNettyClient;
//    }
//
//    @Override
//    public void operationComplete(ChannelFuture channelFuture) throws Exception {
////        if (channelFuture.isSuccess()){
////            log.info("客户端链接成功 {}:{}",mainNodeIp,mainNodePort);
////            retries = 0;
////        }else {
////            boolean allowRetry = retryPolicy.allowRetry(retries);
////            if (allowRetry){
////                log.error("客户端未连接，现在重新链接 {}:{}",mainNodeIp,mainNodePort);
////                long sleepTimeMs = retryPolicy.getSleepTimeMs(retries);
////                log.info("netty尝试重连次数:{} 等待时间:{}",++retries,sleepTimeMs);
////                final EventLoop loop = channelFuture.channel().eventLoop();
////                loop.schedule(() -> {
////                    abstractNettyClient.connect(mainNodeIp, mainNodePort);
////                }, sleepTimeMs, TimeUnit.MILLISECONDS);
////            }
////        }
//    }
//
//}
