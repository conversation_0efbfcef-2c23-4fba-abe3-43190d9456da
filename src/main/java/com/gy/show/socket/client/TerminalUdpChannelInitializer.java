package com.gy.show.socket.client;

import com.gy.show.socket.handler.MetricsHandler;
import com.gy.show.socket.handler.TerminalMessageDecodeHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.springframework.stereotype.Component;

@Component
public class TerminalUdpChannelInitializer extends ChannelInitializer<NioDatagramChannel> {

    @Override
    protected void initChannel(NioDatagramChannel channel) throws Exception {
        // 在管道中添加我们自己的接收数据实现方法
        channel.pipeline().addLast(new MetricsHandler());
        channel.pipeline().addLast(new TerminalMessageDecodeHandler());
    }

}
