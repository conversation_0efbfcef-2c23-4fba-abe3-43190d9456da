package com.gy.show.socket.client;

import com.gy.show.socket.handler.StationDataDecodeHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.nio.NioSocketChannel;
import org.springframework.stereotype.Component;

@Component
public class StationTcpChannelInitializer extends ChannelInitializer<NioSocketChannel> {

    @Override
    protected void initChannel(NioSocketChannel channel) throws Exception {
        // 在管道中添加我们自己的接收数据实现方法
//        channel.pipeline().addLast(new StationSpaceTelemetryDecoder());
//        channel.pipeline().addLast(new GenericEncoder());
        channel.pipeline().addLast(new StationDataDecodeHandler());
    }

}
