package com.gy.show.socket.client;

import com.gy.show.socket.handler.MetricsHandler;
import com.gy.show.socket.handler.StationSpaceStatusDecodeHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.springframework.stereotype.Component;

@Component
public class StationStatusUdpChannelInitializer extends ChannelInitializer<NioDatagramChannel> {

    @Override
    protected void initChannel(NioDatagramChannel channel) throws Exception {
        channel.pipeline().addLast(new MetricsHandler());
        channel.pipeline().addLast(new StationSpaceStatusDecodeHandler());
    }

}
