package com.gy.show.socket.client;

import com.gy.show.common.ServiceException;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class StationTcpClient {

    protected Channel channel;

    //配置服务端NIO线程组
    protected EventLoopGroup workerGroup = new NioEventLoopGroup();

    protected ChannelFuture channelFuture;

    public ChannelFuture connect(String host, int port) {
        try {
            Bootstrap b = new Bootstrap();
            b.group(workerGroup);
            b.channel(NioSocketChannel.class);
            b.option(ChannelOption.AUTO_READ, true);
            b.handler(createHandler());

            // 异步操作
            channelFuture = b
                    .connect(host, port)
                    // 连接时重连5次
//                    .addListener(new ConnectionListener(this).
//                            setRetryPolicy(new ExponentialBackOffRetry(Constants.BASE_SLEEP_TIMES_MS, Constants.MAX_RETRIES)))
                    .sync();// netty 启动时如果连接失败，会断线重连

            // 关闭客户端
            channel = channelFuture.channel();

            channel.closeFuture().addListener(future -> destroy());
        } catch (Exception e) {
            log.error("Netty error", e);
            destroy();
        }
        return channelFuture;
    }

    protected ChannelInitializer<NioSocketChannel> createHandler() {
        return new StationTcpChannelInitializer();
    }

    public void sendMessage(ByteBuf byteBuf) {
        if (channel == null) {
            throw new ServiceException("Tcp通道未打开");

        }
//        byte[] data = new byte[byteBuf.readableBytes()];
//        byteBuf.readBytes(data);
        channel.writeAndFlush(byteBuf);
        log.info("发送数据成功");
    }

    public void destroy() {
        log.info("Netty客户端连接关闭");
        if (channel != null && channel.isOpen()) {
            workerGroup.shutdownGracefully();
        }
    }
}
