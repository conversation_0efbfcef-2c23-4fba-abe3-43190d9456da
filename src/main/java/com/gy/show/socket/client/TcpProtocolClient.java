//package com.gy.show.socket.client;
//
//import io.netty.bootstrap.Bootstrap;
//import io.netty.channel.ChannelFuture;
//import io.netty.channel.ChannelInitializer;
//import io.netty.channel.ChannelOption;
//import io.netty.channel.socket.nio.NioDatagramChannel;
//import io.netty.channel.socket.nio.NioSocketChannel;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//public class TcpProtocolClient extends AbstractNettyClient {
//
//    @Override
//    public ChannelFuture connect(String host, int port) {
//        try {
//            Bootstrap b = new Bootstrap();
//            b.group(workerGroup);
//            b.channel(NioSocketChannel.class);
//            b.option(ChannelOption.AUTO_READ, true);
//            b.handler(createHandler());
//
//            // 异步操作
//            channelFuture = b
//                    .connect(host, port)
//                    // 连接时重连5次
////                    .addListener(new ConnectionListener(this).
////                            setRetryPolicy(new ExponentialBackOffRetry(Constants.BASE_SLEEP_TIMES_MS, Constants.MAX_RETRIES)))
//                    .sync();// netty 启动时如果连接失败，会断线重连
//
//            // 关闭客户端
//            channelFuture.channel()
//                    .closeFuture().addListener(future -> destroy());
//        } catch (Exception e) {
//            log.error("Netty error", e);
//            destroy();
//        }
//        return channelFuture;
//    }
//
//    protected ChannelInitializer<NioDatagramChannel> createHandler() {
//        return new ControlUdpChannelInitializer();
//    }
//}
