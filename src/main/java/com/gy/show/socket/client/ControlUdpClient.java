package com.gy.show.socket.client;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.nio.NioDatagramChannel;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ControlUdpClient extends AbstractNettyClient {

//    private String localIp;

    @Override
//    public ChannelFuture connect(String ip, int port) {
//        // 组播地址
//        InetSocketAddress groupAddress = new InetSocketAddress(ip, port);
//
//        ChannelFuture future = null;
//        try {
//            NetworkInterface ni = NetworkInterface.getByInetAddress(InetAddress.getByName(localIp));
//            Enumeration<InetAddress> addresses = ni.getInetAddresses();
//
//            InetAddress localAddress = null;
//            while (addresses.hasMoreElements()) {
//                InetAddress address = addresses.nextElement();
//                if (address instanceof Inet4Address) {
//                    localAddress = address;
//                    log.info("网络接口名称为：{}", ni.getName());
//                    log.info("网卡接口地址：{}", address.getHostAddress());
//                }
//            }
//
//            Bootstrap b = new Bootstrap();
//            b.group(workerGroup);
//            b.channelFactory((ChannelFactory<NioDatagramChannel>) () -> new NioDatagramChannel(InternetProtocolFamily.IPv4));
//            b.localAddress(new InetSocketAddress(localAddress, port));
//            b.option(ChannelOption.IP_MULTICAST_IF, ni);
//            b.option(ChannelOption.IP_MULTICAST_ADDR, InetAddress.getByName(localIp));
//            b.option(ChannelOption.SO_REUSEADDR, true);
//            b.option(ChannelOption.SO_RCVBUF, 2048 * 1024);
//            b.option(ChannelOption.SO_SNDBUF, 1024 * 1024);
////            b.channel(NioDatagramChannel.class);
//            b.handler(createHandler());
//
//            future = b.bind(port).sync();
//            NioDatagramChannel datagramChannel = (NioDatagramChannel) future.channel();
//
////            datagramChannel.joinGroup(groupAddress, ni).sync();
//
//            datagramChannel.closeFuture().addListener(f -> destroy());
//        } catch (Exception e) {
//            log.error("加入UDP组播失败", e);
//            destroy();
//        }
//        return future;
//    }

    protected ChannelInitializer<NioDatagramChannel> createHandler() {
        return new ControlUdpChannelInitializer();
    }
}
