package com.gy.show.socket;


import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BinaryParser {

    /**
     * 解析报文头部
     *
     * @param byteBuf ByteBuf 对象
     * @param headerDefinitions 报文头部字段定义
     * @return 解析的头部字段值
     */
    public static Map<String, Object> parseFields(ByteBuf byteBuf, List<FieldDefinition> headerDefinitions) {
        Map<String, Object> headerMap = new HashMap<>();
        for (FieldDefinition definition : headerDefinitions) {
            Object value = definition.parseField(byteBuf);
            headerMap.put(definition.getName(), value);
        }
        return headerMap;
    }

    public static Map<String, Object> parseFieldsLE(ByteBuf byteBuf, List<FieldDefinition> headerDefinitions) {
        Map<String, Object> headerMap = new HashMap<>();
        for (FieldDefinition definition : headerDefinitions) {
            Object value = definition.parseFieldLE(byteBuf);
            headerMap.put(definition.getName(), value);
        }
        return headerMap;
    }

    /**
     * 解析报文体
     *
     * @param byteBuf ByteBuf 对象
     * @param bodyDefinitions 报文体字段定义
     * @return 解析的报文体字段值
     */
//    public static Map<String, Object> parseBody(ByteBuf byteBuf, List<FieldDefinition> bodyDefinitions) {
//        Map<String, Object> bodyMap = new HashMap<>();
//        for (FieldDefinition definition : bodyDefinitions) {
//            Object value = definition.parseField(byteBuf);
//            bodyMap.put(definition.getName(), value);
//        }
//        return bodyMap;
//    }

    /**
     * 解析多位字段
     *
     * @param byteBuf ByteBuf 对象
     * @param byteIndex 字节索引
     * @param bitDefinitions 多位字段定义
     * @return 按位解析的字段结果
     */
//    public static Map<String, String> parseMultiBitFields(ByteBuf byteBuf, int byteIndex, List<BitField> bitDefinitions) {
//        Map<String, String> result = new HashMap<>();
//        byte currentByte = byteBuf.getByte(byteBuf.readerIndex() + byteIndex);
//
//        for (BitField field : bitDefinitions) {
//            int value = extractBits(currentByte, field.getStartBit(), field.getBitLength());
//            String mappedValue = field.getValues().getOrDefault(value, "UNKNOWN");
//            result.put(field.getName(), mappedValue);
//        }
//
//        return result;
//    }

    /**
     * 提取指定范围的位值
     */
    private static int extractBits(byte currentByte, int startBit, int bitLength) {
        int bitMask = (1 << bitLength) - 1;
        return (currentByte >> startBit) & bitMask;
    }

//    /**
//     * 解析 ByteBuf 中的二进制数据
//     *
//     * @param byteBuf          ByteBuf 对象
//     * @param fieldDefinitions 字段定义列表
//     * @return 字段名到值的映射
//     */
//    public static Map<String, Object> parse(ByteBuf byteBuf, List<FieldDefinition> fieldDefinitions) {
//        Map<String, Object> result = new HashMap<>();
//        int bitCursor = 0; // 按位解析时的当前位位置
//
//        for (FieldDefinition field : fieldDefinitions) {
//            Object value = null;
//
//            if (field.getBitLength() > 0) { // 按位解析
////                value = parseBits(byteBuf, field.getBitLength(), bitCursor);
//                bitCursor += field.getBitLength();
//            } else { // 按字节解析
//                if (field.getType() == Integer.class) {
//                    value = byteBuf.readInt();
//                } else if (field.getType() == Short.class) {
//                    value = byteBuf.readShort();
//                } else if (field.getType() == Long.class) {
//                    value = byteBuf.readLong();
//                } else if (field.getType() == String.class) {
//                    byte[] strBytes = new byte[field.getLength()];
//                    byteBuf.readBytes(strBytes);
//                    value = new String(strBytes, StandardCharsets.UTF_8).trim();
//                } else if (field.getType() == Byte.class) {
//                    value = byteBuf.readByte();
//                } else {
//                    throw new IllegalArgumentException("Unsupported type: " + field.getType());
//                }
//            }
//
//            result.put(field.getName(), value);
//        }
//
//        return result;
//    }
//
//    /**
//     * 解析 ByteBuf 中的某个字节的指定位值。
//     *
//     * @param byteBuf ByteBuf 对象
//     * @param byteIndex 要解析的字节索引（从 ByteBuf 起始位置算起）
//     * @param bitFields 位字段定义（key 为位名，value 为位掩码）
//     * @return 每个位名对应的值（true 表示该位为 1，false 表示该位为 0）
//     */
//    public static Map<String, Boolean> parseBitsFromByte(ByteBuf byteBuf, int byteIndex, Map<String, Integer> bitFields) {
//        Map<String, Boolean> result = new HashMap<>();
//        byte currentByte = byteBuf.getByte(byteBuf.readerIndex() + byteIndex);
//
//        // 遍历位字段定义，逐位解析
//        for (Map.Entry<String, Integer> entry : bitFields.entrySet()) {
//            String bitName = entry.getKey();
//            int bitMask = entry.getValue();
//
//            // 使用位运算提取位值
//            boolean isBitSet = (currentByte & bitMask) != 0;
//            result.put(bitName, isBitSet);
//        }
//
//        return result;
//    }
//
//    private static void readBits(byte value) {
//        for (int i = 7; i >= 0; i--) {
//            int bit = (value >> i) & 1;
//            System.out.println("Bit " + i + ": " + bit);
//        }
//    }
//
//    /**
//     * 从指定字节中提取多位范围的数值。
//     *
//     * @param currentByte 当前字节
//     * @param startBit 起始位（从 0 开始）
//     * @param bitLength 位长度
//     * @return 提取的多位值
//     */
//    public static int extractBits(byte currentByte, int startBit, int bitLength) {
//        int bitMask = (1 << bitLength) - 1; // 构造位掩码
//        return (currentByte >> startBit) & bitMask;
//    }


    public static void main(String[] args) {
//        byte value = (byte) 0b10101111;
//        readBits(value);

        // 定义字段结构：字段名、类型、字节长度、位长度
//        List<FieldDefinition> fields = Arrays.asList(
//                new FieldDefinition("header", String.class, 4, 0), // 4 字节字符串
//                new FieldDefinition("flag1", Integer.class, 0, 3), // 3 位整数
//                new FieldDefinition("flag2", Integer.class, 0, 5), // 5 位整数
//                new FieldDefinition("length", Short.class, 2, 0),  // 2 字节短整型
//                new FieldDefinition("flag3", Integer.class, 0, 4), // 4 位整数
//                new FieldDefinition("flag4", Integer.class, 0, 4)  // 4 位整数
//        );
//
//        // 模拟输入的二进制数据
//        ByteBuf byteBuf = Unpooled.buffer();
//        byteBuf.writeBytes("HEAD".getBytes(StandardCharsets.UTF_8)); // 4 字节字符串
//        byteBuf.writeByte(0b10101111); // 8 位的标志位
//        byteBuf.writeShort(100);       // 2 字节短整型
//        byteBuf.writeByte(0b11110000); // 8 位的标志位
//
//        // 解析数据
//        Map<String, Object> result = BinaryParser.parse(byteBuf, fields);
//
//        // 打印解析结果
//        result.forEach((key, value) -> System.out.println(key + ": " + value));


        // 创建一个测试字节缓冲区
//        ByteBuf byteBuf = Unpooled.buffer();
//        byteBuf.writeByte(0b10101011); // 1 字节数据：bit0=1, bit1=1, bit2=0, ...
//
//        // 定义位字段：字段名 -> 位掩码
//        Map<String, Integer> bitFields = MapUtil.of(
//                new Pair<>("bit0_a", 0b00000001), // bit0
//                new Pair<>("bit1_b", 0b00000010), // bit1
//                new Pair<>("bit2_c", 0b00000100), // bit2
//                new Pair<>("bit3_d", 0b00001000), // bit3
//                new Pair<>("bit4_e", 0b00010000), // bit4
//                new Pair<>("bit7_h", 0b10000000)  // bit7
//        );
//
//        // 按位解析第 0 个字节
//        Map<String, Boolean> result = BinaryParser.parseBitsFromByte(byteBuf, 0, bitFields);
//
//        // 打印解析结果
//        result.forEach((key, value) -> System.out.println(key + ": " + value));
//
//
//        // 解析 bit0-bit2 的数值
//        int value = BinaryParser.extractBits((byte) 0b10101011, 0, 3);
//        System.out.println("bit0-bit2 value: " + value); // 输出 3 (0b011)

        String a = "目标代号1";
        byte[] bytes = a.getBytes(StandardCharsets.UTF_8);

        for (byte aByte : bytes) {
            System.out.println(aByte);
        }

        System.out.println(new String(bytes));
    }
}

