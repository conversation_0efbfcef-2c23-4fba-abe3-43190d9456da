package com.gy.show.socket.codec;

import java.util.HashMap;
import java.util.Map;

public class ProtocolRegistry {

    private static final Map<Integer, Class<?>> registry = new HashMap<>();

    // 注册报文
    public static void register(int type, Class<?> clazz) {
        registry.put(type, clazz);
    }

    // 获取报文类
    public static Class<?> getMessageClass(int type) {
        return registry.get(type);
    }
}
