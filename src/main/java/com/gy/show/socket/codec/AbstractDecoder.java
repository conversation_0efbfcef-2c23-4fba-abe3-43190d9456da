package com.gy.show.socket.codec;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;

import java.lang.reflect.Field;
import java.nio.ByteOrder;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

public abstract class AbstractDecoder<T> extends MessageToMessageDecoder<ByteBuf> {

    private final Class<T> messageClass;

    public AbstractDecoder(Class<T> messageClass) {
        this.messageClass = messageClass;
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 如果当前解码器不匹配报文类型，则直接忽略
        if (!isMatch(in)) {
            return;
        }

        // 解析报文
        T instance = decodeBody(in);
        if (instance != null) {
            out.add(instance);
        }
    }

    /**
     * 子类实现，用于判断是否匹配当前解码器。
     *
     * @param in 输入的 ByteBuf
     * @return true 如果匹配，false 否则
     */
    protected abstract boolean isMatch(ByteBuf in);

    /**
     * 子类可自定义解码逻辑，默认为反射解析字段。
     *
     * @param in 输入的 ByteBuf
     * @return 解码后的对象
     */
    protected T decodeBody(ByteBuf in) throws Exception {
        T instance = messageClass.getDeclaredConstructor().newInstance();
        Field[] fields = messageClass.getDeclaredFields();

        // 根据字段顺序解析
        Field[] orderedFields = Arrays.stream(fields)
                .filter(field -> field.isAnnotationPresent(ProtocolField.class))
                .sorted(Comparator.comparingInt(field -> field.getAnnotation(ProtocolField.class).order()))
                .toArray(Field[]::new);

        for (Field field : orderedFields) {
            ProtocolField annotation = field.getAnnotation(ProtocolField.class);
            field.setAccessible(true);
            String typeName = annotation.type();
            String endian = annotation.endian();
            ByteOrder order = "little".equalsIgnoreCase(endian) ? ByteOrder.LITTLE_ENDIAN : ByteOrder.BIG_ENDIAN;

            // 解析字段值
            switch (typeName.toLowerCase()) {
                case "int":
                    field.set(instance, order == ByteOrder.BIG_ENDIAN ? in.readInt() : Integer.reverseBytes(in.readInt()));
                    break;
                case "short":
                    field.set(instance, order == ByteOrder.BIG_ENDIAN ? in.readShort() : Short.reverseBytes(in.readShort()));
                    break;
                case "long":
                    field.set(instance, order == ByteOrder.BIG_ENDIAN ? in.readLong() : Long.reverseBytes(in.readLong()));
                    break;
                case "byte":
                    field.set(instance, in.readByte());
                    break;
                case "bytes":
                    byte[] data = new byte[in.readableBytes()];
                    in.readBytes(data);
                    field.set(instance, data);
                    break;
                case "string":
                    int length = in.readInt(); // 假设字符串前包含长度字段
                    byte[] bytes = new byte[length];
                    in.readBytes(bytes);
                    field.set(instance, new String(bytes));
                    break;
                case "boolean":
                    field.set(instance, in.readBoolean());
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported type: " + typeName);
            }
        }

        return instance;
    }
}
