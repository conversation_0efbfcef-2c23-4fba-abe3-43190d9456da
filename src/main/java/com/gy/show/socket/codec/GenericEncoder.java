package com.gy.show.socket.codec;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageEncoder;

import java.lang.reflect.Field;
import java.nio.ByteOrder;
import java.util.List;

public class GenericEncoder extends MessageToMessageEncoder<Object> {

    @Override
    protected void encode(ChannelHandlerContext ctx, Object msg, List<Object> out) throws Exception {
        ByteBuf buffer = ctx.alloc().buffer();
        Field[] fields = msg.getClass().getDeclaredFields();

        for (Field field : fields) {
            ProtocolField annotation = field.getAnnotation(ProtocolField.class);
            if (annotation == null) continue;

            field.setAccessible(true);
            Object value = field.get(msg);
            String typeName = annotation.type();
            String endian = annotation.endian();

            // 根据大小端写入数据
            ByteOrder order = "little".equalsIgnoreCase(endian) ? ByteOrder.LITTLE_ENDIAN : ByteOrder.BIG_ENDIAN;

            switch (typeName.toLowerCase()) {
                case "int":
                    buffer.writeInt(order == ByteOrder.BIG_ENDIAN ? (Integer) value : Integer.reverseBytes((Integer) value));
                    break;
                case "short":
                    buffer.writeShort(order == ByteOrder.BIG_ENDIAN ? (Short) value : Short.reverseBytes((Short) value));
                    break;
                case "long":
                    buffer.writeLong(order == ByteOrder.BIG_ENDIAN ? (Long) value : Long.reverseBytes((Long) value));
                    break;
                case "byte":
                    buffer.writeByte((Byte) value);
                    break;
                case "string":
                    byte[] bytes = ((String) value).getBytes();
                    buffer.writeInt(bytes.length);
                    buffer.writeBytes(bytes);
                    break;
                case "boolean":
                    buffer.writeBoolean((Boolean) value);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported type: " + typeName);
            }
        }

        out.add(buffer);
    }
}

