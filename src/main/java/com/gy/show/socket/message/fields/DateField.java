package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

import java.time.LocalDateTime;

/**
 * 7个字节
 * YYYY-MM-DDTHH:MM:SS
 * 2B：YYYY年
 * 1B：MM月
 * 1B：DD日
 * 1B：HH小时
 * 1B：MM分钟
 * 1B：秒
 */
public class DateField extends FieldDefinition {

    public DateField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        short year = byteBuf.readShort();
        byte month = byteBuf.readByte();
        byte day = byteBuf.readByte();
        byte hour = byteBuf.readByte();
        byte minute = byteBuf.readByte();
        byte second = byteBuf.readByte();

        StringBuilder sb = new StringBuilder();
        sb.append(year)
                .append("-")
                .append(convertNumber(month))
                .append("-")
                .append(convertNumber(day))
                .append(" ")
                .append(convertNumber(hour))
                .append(":")
                .append(convertNumber(minute))
                .append(":")
                .append(convertNumber(second));
        return sb.toString();
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return null;
    }

    private String convertNumber(byte b) {
        return String.format("%02d", b);
    }

    public static void writeField(ByteBuf byteBuf, Object value) {
        LocalDateTime date = (LocalDateTime) value;

        byteBuf.writeShort(date.getYear());
        byteBuf.writeByte(date.getMonthValue());
        byteBuf.writeByte(date.getDayOfMonth());

        byteBuf.writeByte(date.getHour());
        byteBuf.writeByte(date.getMinute());
        byteBuf.writeByte(date.getSecond());
    }

}
