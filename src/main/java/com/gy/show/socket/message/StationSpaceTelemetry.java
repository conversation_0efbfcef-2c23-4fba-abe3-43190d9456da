package com.gy.show.socket.message;

import com.gy.show.socket.codec.ProtocolField;
import lombok.Data;

/**
 * 航天测控站遥测报文实体类
 */
@Data
public class StationSpaceTelemetry extends StationSpaceDataHead {

    @ProtocolField(order = 7, type = "byte")
    private byte flag;

    @ProtocolField(order = 8, type = "int", endian = "little")
    private Integer ty;

    @ProtocolField(order = 8, type = "bytes")
    private byte[] data;

}
