package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.util.HashMap;
import java.util.Map;

/**
 * 用于做需要转义字段的解析
 */
public class CharField extends FieldDefinition {

    private final Map<String, Integer> charFields; // 位字段定义：key 为中文意，value 为对应代码

    public CharField(String name, Map<String, Integer> charFields) {
        super(name);
        this.charFields = charFields;
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        byte targetCode = byteBuf.readByte();

        for (Map.Entry<String, Integer> entry : charFields.entrySet()) {
            Integer code = entry.getValue();

            if (code.equals((int) targetCode)) {
                return entry.getKey();
            }
        }
        return targetCode;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return null;
    }

    public static void main(String[] args) {
        Map<String, Integer> values = new HashMap<>();

        values.put("CAN总线通讯", 1);
        values.put("能源系统", 2);
        values.put("发电机组（APU）", 3);

        CharField tes = new CharField("tes", values);

        ByteBuf buffer = Unpooled.buffer();
        buffer.writeByte(0x00);

        System.out.println(tes.parseField(buffer));
    }

}
