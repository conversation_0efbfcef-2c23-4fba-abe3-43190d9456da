package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class ShortField extends FieldDefinition {

    public ShortField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        int s = byteBuf.readShort();
        return s;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return byteBuf.readShortLE();
    }

    public static void writeField(ByteBuf byteBuf, Object value) {
        byteBuf.writeShort(Short.parseShort(value.toString()));
    }
}
