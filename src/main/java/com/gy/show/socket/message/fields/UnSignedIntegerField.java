package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class UnSignedIntegerField extends FieldDefinition {

    public UnSignedIntegerField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        return byteBuf.readUnsignedInt();
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return byteBuf.readUnsignedIntLE();
    }

    public static void writeField(ByteBuf byteBuf, Object value) {
        byteBuf.writeInt(Integer.parseInt(value.toString()));
    }
}
