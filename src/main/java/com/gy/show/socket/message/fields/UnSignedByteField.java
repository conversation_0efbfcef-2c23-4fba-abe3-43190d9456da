package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class UnSignedByteField extends FieldDefinition {

    public UnSignedByteField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        short s =  byteBuf.readUnsignedByte();
        return s;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return parseField(byteBuf);
    }

    public static void writeField(ByteBuf byteBuf, Object value) {
//        byte byteValue = (byte) Integer.parseInt(value.toString(), 16);

        byteBuf.writeByte((byte) value);
    }
}
