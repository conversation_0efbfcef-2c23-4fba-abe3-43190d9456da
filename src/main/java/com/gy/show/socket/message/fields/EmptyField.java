package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class EmptyField extends FieldDefinition {

    private int skipLength;

    public EmptyField(String name, int skipLength) {
        super(name);
        this.skipLength = skipLength;
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        byteBuf.skipBytes(skipLength);
        return 0;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        byteBuf.skipBytes(skipLength);
        return 0;
    }

}
