package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class IntegerField extends FieldDefinition {

    public IntegerField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        Integer s = byteBuf.readInt();
        return s;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return byteBuf.readIntLE();
    }

    public static void writeField(ByteBuf byteBuf, Object value) {
        byteBuf.writeInt(Integer.parseInt(value.toString()));
    }
}
