package com.gy.show.socket.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class HeadMessage {

    // 包序号 2字节
    private short seq;

    // 信源 2字节
    private short source;

    // 信宿 2字节
    private short sink;

    // 包类型 1字节
    private byte type;

    // 包长度 2字节
    private short length;

    // 分段数 1字节
    private byte segments;

    // 分段号 1字节
    private byte segmentNum;

    // 预留
    private byte[] reserve;
}
