//package com.gy.show.socket.message.fields;
//
//import com.gy.show.socket.message.FieldDefinition;
//import io.netty.buffer.ByteBuf;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Optional;
//
///**
// * 按位进行解析
// */
//public class BitField extends FieldDefinition {
//    private final Map<Integer, String> values; // 位值到含义的映射
//
//    public BitField(String name, Map<Integer, String> values) {
//        super(name); // 调用父类的构造器
//        this.values = values;
//    }
//
//    @Override
//    public Object parseField(ByteBuf byteBuf) {
//        // 假设字节字段从当前 ByteBuf 的第一个字节读取
//        byte currentByte = byteBuf.readByte();
//        // 结果存储
//        Map<String, Object> result = new HashMap<>();
//
//
//        // 逐位解析
//        boolean bit0 = (currentByte & 0b00000001) != 0;
//        boolean bit1 = (currentByte & 0b00000010) != 0;
//        boolean bit2 = (currentByte & 0b00000100) != 0;
//        boolean bit3 = (currentByte & 0b00001000) != 0;
//        boolean bit4 = (currentByte & 0b00010000) != 0;
//        boolean bit5 = (currentByte & 0b00100000) != 0;
//        boolean bit6 = (currentByte & 0b01000000) != 0;
//        boolean bit7 = (currentByte & 0b10000000) != 0;
//
//        Optional.ofNullable(values.get(0b00000001)).ifPresent(k -> result.put(k, bit0));
//        Optional.ofNullable(values.get(0b00000010)).ifPresent(k -> result.put(k, bit1));
//        Optional.ofNullable(values.get(0b00000100)).ifPresent(k -> result.put(k, bit2));
//        Optional.ofNullable(values.get(0b00001000)).ifPresent(k -> result.put(k, bit3));
//        Optional.ofNullable(values.get(0b00010000)).ifPresent(k -> result.put(k, bit4));
//        Optional.ofNullable(values.get(0b00100000)).ifPresent(k -> result.put(k, bit5));
//        Optional.ofNullable(values.get(0b01000000)).ifPresent(k -> result.put(k, bit6));
//        Optional.ofNullable(values.get(0b10000000)).ifPresent(k -> result.put(k, bit7));
//
//        // 根据映射获取对应含义
//        return result;
//    }
//
//}
//
