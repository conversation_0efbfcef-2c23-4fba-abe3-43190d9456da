package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class FloatField extends FieldDefinition {

    public FloatField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        float s = byteBuf.readFloat();
        return s;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return byteBuf.readFloatLE();
    }

    public static void writeField(ByteBuf byteBuf, Object value) {
        byteBuf.writeFloat(Float.parseFloat(value.toString()));
    }
}
