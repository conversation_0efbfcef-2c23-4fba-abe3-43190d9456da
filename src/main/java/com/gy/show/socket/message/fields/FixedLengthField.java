package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class FixedLengthField extends FieldDefinition {
    private final int length;

    public FixedLengthField(String name, int length) {
        super(name);
        this.length = length;
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        byte[] value = new byte[length];
        byteBuf.readBytes(value);
        return value;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return parseField(byteBuf);
    }

}
