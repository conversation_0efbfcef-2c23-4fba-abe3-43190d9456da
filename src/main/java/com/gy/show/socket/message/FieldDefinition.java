package com.gy.show.socket.message;

import io.netty.buffer.ByteBuf;

import java.util.HashMap;
import java.util.Map;

public abstract class FieldDefinition {

    private String name;       // 字段名

    public static final Map<String, Object> platformState = new HashMap<>();

    static {
        platformState.put("000", "空挡");
        platformState.put("001", "前进低挡");
        platformState.put("010", "倒车低挡");
        platformState.put("011", "前进中挡");
        platformState.put("100", "倒车中挡");
        platformState.put("101", "前进高挡");
        platformState.put("110", "倒车高挡");
        platformState.put("111", "中心转向");
    }

    public FieldDefinition(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public abstract Object parseField(ByteBuf byteBuf);

    public abstract Object parseFieldLE(ByteBuf byteBuf);

    public static String byteToHex(byte byt) {
        return String.format("%02X", byt);
    }

}
