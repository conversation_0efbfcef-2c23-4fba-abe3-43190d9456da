package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

/**
 * 坐标信息字段，需特殊处理
 */
public class AngleField extends FieldDefinition {

    private static final Integer angle = 10;

    public AngleField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        int i = byteBuf.readInt();
        return (double)i / angle;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        int i = byteBuf.readIntLE();
        return (double)i / angle;
    }

}
