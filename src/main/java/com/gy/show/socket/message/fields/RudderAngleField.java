package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

/**
 * 坐标信息字段，需特殊处理
 */
public class RudderAngleField extends FieldDefinition {

    private static final Integer angle = 1000;

    public RudderAngleField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        int i = byteBuf.readInt();
        return ((double)i / angle) * 30;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        int i = byteBuf.readIntLE();
        return ((double)i / angle) * 30;
    }

}
