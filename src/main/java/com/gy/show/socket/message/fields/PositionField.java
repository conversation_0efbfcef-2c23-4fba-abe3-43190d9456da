package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

/**
 * 坐标信息字段，需特殊处理
 */
public class PositionField extends FieldDefinition {

    private static final Integer position = 10000000;

    public PositionField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        int i = byteBuf.readInt();
        return (double)i / position;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        int i = byteBuf.readIntLE();
        return (double)i / position;
    }

}
