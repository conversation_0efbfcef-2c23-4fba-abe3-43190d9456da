package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class DoubleField extends FieldDefinition {

    private final int length;

    public DoubleField(String name, int length) {
        super(name);
        this.length = length;
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        short i = byteBuf.readShort();
        return (double)i / length;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        short i = byteBuf.readShortLE();
        return (double)i / length;
    }

}
