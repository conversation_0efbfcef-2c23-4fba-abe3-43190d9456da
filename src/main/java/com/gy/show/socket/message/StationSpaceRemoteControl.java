package com.gy.show.socket.message;

import com.gy.show.socket.codec.ProtocolField;
import lombok.Data;

/**
 * 航天测控站遥测报文实体类
 */
@Data
public class StationSpaceRemoteControl extends StationSpaceDataHead {

    @ProtocolField(order = 7, type = "byte")
    private byte flag;

    @ProtocolField(order = 8, type = "byte")
    private byte logo;

    @ProtocolField(order = 9, type = "int")
    private Integer number;

    @ProtocolField(order = 10, type = "short")
    private Short date;

    @ProtocolField(order = 11, type = "int")
    private Integer tim;

    @ProtocolField(order = 12, type = "bytes")
    private byte[] data;

}
