package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

/**
 * 坐标信息字段，需特殊处理
 */
public class RadianField extends FieldDefinition {

    private static final Integer position = 1000;

    public RadianField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        int i = byteBuf.readInt();
        return Math.round((((double)i / position) * 180) / Math.PI);
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        int i = byteBuf.readIntLE();
        return Math.round((((double)i / position) * 180) / Math.PI);
    }

}
