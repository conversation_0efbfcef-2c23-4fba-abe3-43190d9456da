package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import com.gy.show.util.DateUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

/**
 * 4字节BCD码，‘YYMD’，如2000年12月12日表示成0x20001212
 */
public class DateLEField extends FieldDefinition {

    public DateLEField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        return null;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return null;
    }

    public static void writeDateField(ByteBuf byteBuf, Object value) {
        LocalDate date = (LocalDate) value;

        // 提取年、月、日
        int year = date.getYear();
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();

        if (year < 0 || year > 9999) {
            throw new IllegalArgumentException("Year out of range for BCD encoding (0000-9999)");
        }

        // 正确的 BCD 编码（按位拆分并组合）
        byte byte1 = (byte) ((year / 100 / 10 << 4) | (year / 100 % 10));
        byte byte2 = (byte) ((year % 100 / 10 << 4) | (year % 100 % 10));
        byte byte3 = (byte) ((month / 10 << 4) | (month % 10));
        byte byte4 = (byte) ((day / 10 << 4) | (day % 10));

        byteBuf.writeByte(byte4);
        byteBuf.writeByte(byte3);
        byteBuf.writeByte(byte2);
        byteBuf.writeByte(byte1);
    }

    public static void writeTimeField(ByteBuf byteBuf, Object value) {
        long daySeconds = DateUtil.getDaySeconds();

        byteBuf.writeIntLE((int) daySeconds);
    }

    public static void main(String[] args) {
//        // 示例日期
//        LocalDate date = LocalDate.of(2000, 12, 12);
//
//        // 创建 ByteBuf
//        ByteBuf byteBuf = Unpooled.buffer();
//
//        // 编码日期
//        writeDateField(byteBuf, date);
//
//        // 读取并验证结果
//        int encodedDate = byteBuf.readIntLE();
//        System.out.printf("Encoded BCD Date (Hex): 0x%08X\n", encodedDate); // 输出: 0x20001212
        System.out.println(new Date(1739772605123l));
    }

}
