package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

import java.util.HashMap;
import java.util.Map;

public class UnSingleBitField extends FieldDefinition {

    private final Map<String, Integer> bitFields; // 位字段定义：key 为位名，value 为位掩码

    public UnSingleBitField(String name, Map<String, Integer> bitFields) {
        super(name);
        this.bitFields = bitFields;
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        Map<String, Boolean> result = new HashMap<>();
        byte currentByte = byteBuf.readByte();

        // 遍历位字段定义，逐位解析
        for (Map.Entry<String, Integer> entry : bitFields.entrySet()) {
            String bitName = entry.getKey();
            int bitMask = entry.getValue();

            // 使用位运算提取位值
            boolean isBitSet = (currentByte & bitMask) == 0;
            result.put(bitName, isBitSet);
        }

        return result;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return null;
    }

}
