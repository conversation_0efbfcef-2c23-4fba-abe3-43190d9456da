package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class UavPositionField extends FieldDefinition {

    private static final Integer position = 100;

    public UavPositionField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        int i = byteBuf.readInt();
        return (double)i / position;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        int i = byteBuf.readIntLE();
        return (double)i / position;
    }

}
