package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class LongField extends FieldDefinition {

    public LongField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        long s = byteBuf.readLong();
        return s;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return byteBuf.readLongLE();
    }

    public static void writeField(ByteBuf byteBuf, Object value) {
        byteBuf.writeLong(Long.parseLong(value.toString()));
    }
}
