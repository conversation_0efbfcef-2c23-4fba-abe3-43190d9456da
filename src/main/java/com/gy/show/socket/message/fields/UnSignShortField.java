package com.gy.show.socket.message.fields;

import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;

public class UnSignShortField extends FieldDefinition {

    public UnSignShortField(String name) {
        super(name);
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        int s = byteBuf.readUnsignedShort();
        return s;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        int s = byteBuf.readUnsignedShortLE();
        return s;
    }

    public static void writeField(ByteBuf byteBuf, Object value) {
        byteBuf.writeShort(Short.parseShort(value.toString()));
    }
}
