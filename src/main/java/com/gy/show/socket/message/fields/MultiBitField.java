package com.gy.show.socket.message.fields;

import cn.hutool.core.map.MapUtil;
import com.gy.show.socket.message.FieldDefinition;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class MultiBitField extends FieldDefinition {

    private int index;

    public MultiBitField(String name, int index) {
        super(name);
        this.index = index;
    }

    @Override
    public Object parseField(ByteBuf byteBuf) {
        // 获取当前字节
//        byte currentByte = byteBuf.getByte(byteBuf.readerIndex() + index);
        byte currentByte = byteBuf.readByte();

        log.info("平台机动车状态：{}", currentByte);

        // 结果存储
        Map<String, String> result = new HashMap<>();

        // Bit0
        boolean bit0 = (currentByte & 0b00000001) != 0; // 提取 Bit0
        result.put("是否驻车", bit0 ? "驻车" : "非驻车");

        // Bit3~Bit1: a
        Map<Integer, String> bit3ToBit1Mapping = MapUtil.<Integer, String>builder()
                .put( 0b000, "空挡")
                .put(0b001, "前进低挡")
                .put( 0b010, "倒车低挡")
                .put(0b011, "前进中挡")
                .put(0b100, "倒车中挡")
                .put(0b101, "前进高挡")
                .put(0b110, "倒车高挡")
                .put(0b111, "中心转向").build();

        int bit3ToBit1 = (currentByte >> 1) & 0b111; // 提取 Bit3~Bit1
        result.put("当前挡位", bit3ToBit1Mapping.getOrDefault(bit3ToBit1, "a"));

        // Bit4
        boolean bit4 = (currentByte & 0b00010000) != 0; // 提取 Bit4
        result.put("紧急停车状态", bit4 ? "紧急停车中" : "非紧急停车状态");

        // Bit7~Bit5
        Map<Integer, String> bit7ToBit5Mapping = MapUtil.<Integer, String>builder()
                .put( 0b000, "停车")
                .put(0b001, "自主")
                .put( 0b010, "预留")
                .put(0b011, "远程遥控（运载操控车）")
                .put(0b100, "近程遥控")
                .put(0b101, "远程遥控（便携操控端）").build();

        int bit7ToBit5 = (currentByte >> 5) & 0b111; // 提取 Bit7~Bit5
        result.put("当前驾驶模式", bit7ToBit5Mapping.getOrDefault(bit7ToBit5, "l"));

        return result;
    }

    @Override
    public Object parseFieldLE(ByteBuf byteBuf) {
        return null;
    }
}

