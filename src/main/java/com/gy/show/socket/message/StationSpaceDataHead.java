package com.gy.show.socket.message;

import com.gy.show.socket.codec.ProtocolField;
import lombok.Data;

/**
 * 航天测控站数据协议头部
 */
@Data
public class StationSpaceDataHead {

    @ProtocolField(order = 1, type = "int")
    private Integer time;

    @ProtocolField(order = 2, type = "int")
    private Integer taskId;

    @ProtocolField(order = 3, type = "int")
    private Integer typeId;

    @ProtocolField(order = 4, type = "int")
    private Integer deviceId;

    @ProtocolField(order = 5, type = "int")
    private Integer res1;

    @ProtocolField(order = 6, type = "int")
    private Integer length;
}
