package com.gy.show.socket;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.time.LocalTime;

public class UdpTestClient {

    public static void main(String[] args) throws Exception {
        String multicastAddress = "***********"; // 替换为实际多播地址
        int port = 30002; // 替换为实际端口
        InetAddress group = InetAddress.getByName(multicastAddress);
        try (DatagramSocket socket = new DatagramSocket()) {
            for (int i = 0; i < 10000; i++) {
                ByteBuf buffer = Unpooled.buffer();

                // ===== 1. 构造消息头 (28 字节) =====
                // 源地址 (4) + 目的地址 (4) - 全 0
                buffer.writeIntLE(0).writeIntLE(0);
                // 消息类型: F104 (小端: 04 F1 00 00)
                buffer.writeByte(0x04).writeByte(0xF1).writeShortLE(0);
                // 保留字段 (4) - 0
                buffer.writeIntLE(0);
                // 日期: 2024-12-19 (小端)
                int dateValue = 20241219; // YYYYMMDD
                buffer.writeIntLE(dateValue);
                // 时间: 当前时间 (格式 HHmmssSS)
                LocalTime now = LocalTime.now();
                int timeValue = now.getHour() * 1000000 +
                        now.getMinute() * 10000 +
                        now.getSecond() * 100 +
                        now.getNano() / 10_000_000;
                buffer.writeIntLE(timeValue);
                // 长度占位 (稍后填充)
                int lengthPosition = buffer.writerIndex();
                buffer.writeIntLE(0);

                // ===== 2. 构造消息体 (F104 类型) =====
                int bodyStart = buffer.writerIndex();
                // 命令顺序号 (2)
                buffer.writeShortLE(1);

                // 单元 1 (目标公共数据)
                writeUnit(buffer, 1, 38, 54, 4); // 38 头 + 4 组 x 54 字节

                // 单元 2 和 3 (遥测/测量数据)
                writeUnit(buffer, 2, 0, 49, 4); // 4 组 x 49 字节
                writeUnit(buffer, 3, 0, 49, 4);

                // 单元 4 (跳过)
                writeUnit(buffer, 4, 115, 0, 0);

                // 单元 5 和 6 (测距数据)
                writeUnit(buffer, 5, 0, 46, 4); // 4 组 x 46 字节
                writeUnit(buffer, 6, 0, 46, 4);

                // 单元 7 (跳过)
                writeUnit(buffer, 7, 480, 0, 0);

                // 单元 8 (遥测锁定数据)
                writeUnit(buffer, 8, 0, 73, 4); // 4 组 x 73 字节

                // 单元 9-13 (跳过)
                writeUnit(buffer, 9, 16, 0, 0);
                writeUnit(buffer, 10, 16, 0, 0);
                writeUnit(buffer, 11, 232, 0, 0);
                writeUnit(buffer, 12, 260, 0, 0);
                writeUnit(buffer, 13, 60, 0, 0);

                // 回填消息体长度
                int bodyLength = buffer.writerIndex() - bodyStart;
                buffer.setIntLE(lengthPosition, bodyLength);

                // 发送 UDP 数据包

                byte[] bytes = new byte[buffer.readableBytes()];
                buffer.readBytes(bytes);
                DatagramPacket packet = new DatagramPacket(bytes, bytes.length, group, port);
                socket.send(packet);
                System.out.println("Sent test packet to " + multicastAddress + ":" + port);

                Thread.sleep(1000);
            }

        }
    }

    // 辅助方法：写入单元数据
    private static void writeUnit(ByteBuf buf, int unitId, int headerSkip, int itemSize, int itemCount) {
        // 开始标识 (0x55AA -> 小端 AA 55)
        buf.writeByte(0xAA).writeByte(0x55);
        // 头部跳过区域
        if (headerSkip > 0) buf.writeZero(headerSkip);
        // 重复写入目标数据
        for (int i = 0; i < itemCount; i++) {
            buf.writeZero(itemSize); // 填充 0
        }
        // 结束标识
        buf.writeByte(0xAA).writeByte(0x55);
    }
}
