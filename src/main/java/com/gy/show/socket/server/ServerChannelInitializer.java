package com.gy.show.socket.server;

import com.gy.show.socket.BaseChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;

import java.util.concurrent.TimeUnit;

public class ServerChannelInitializer extends BaseChannelInitializer {


    @Override
    protected void initChannel(SocketChannel channel) throws Exception {
        super.initChannel(channel);
        // 在管道中添加我们自己的接收数据实现方法
        // 文件
        channel.pipeline().addLast(new FileTransferServerHandler());
        //空闲监听
        channel.pipeline().addLast(new IdleStateHandler(5000, 5000, 10, TimeUnit.SECONDS));
        channel.pipeline().addLast(new ServerIdleHandler());
    }

}
