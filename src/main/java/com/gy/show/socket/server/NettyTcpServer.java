package com.gy.show.socket.server;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NettyTcpServer {

    public Channel start(int port, int bossThread) {
        Channel channel = null;
        //配置服务端NIO线程组
        EventLoopGroup parentGroup = new NioEventLoopGroup(bossThread); //NioEventLoopGroup extends MultithreadEventLoopGroup Math.max(1, SystemPropertyUtil.getInt("io.runner.eventLoopThreads", NettyRuntime.availableProcessors() * 2));
        EventLoopGroup childGroup = new NioEventLoopGroup();
        try {
            ServerBootstrap b = new ServerBootstrap();

            b.group(parentGroup, childGroup)
                    .channel(NioServerSocketChannel.class)    //非阻塞模式
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .childHandler(new ServerChannelInitializer());
            channel = b.bind(port).sync().channel();

            log.info("Netty tcp server 启动成功. port: {}, bossThread:{}", port, bossThread);
            channel.closeFuture().sync();
        } catch (Exception e) {
            log.error("Netty tcp server 启动异常", e);
        } finally {
            log.info("Netty Server 关闭");
            parentGroup.shutdownGracefully();
            childGroup.shutdownGracefully();
        }
        return channel;
    }

}
