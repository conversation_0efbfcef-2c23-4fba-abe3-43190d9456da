package com.gy.show.socket.server;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ServerIdleHandler extends ChannelInboundHandlerAdapter {

    @Override
    public  void  userEventTriggered(ChannelHandlerContext ctx,Object evt) throws  Exception{
        if (evt instanceof IdleStateEvent){
            IdleStateEvent event=(IdleStateEvent) evt;
            String eventType=null;
            switch (event.state()){
                case READER_IDLE:
                    eventType="读空闲";
                    break;
                case WRITER_IDLE:
                    eventType="写空闲";
                    break;
                case ALL_IDLE:
                    eventType="读写空闲";
                    break;
            }
            log.warn(ctx.channel().remoteAddress()+"--超时时间--"+eventType);
            log.warn("即将关闭netty");
            //发生空闲关闭通道
//            ctx.channel().close();
        }
    }
}
