package com.gy.show.socket.server;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import lombok.extern.slf4j.Slf4j;

import java.net.InetSocketAddress;

@Slf4j
public class UdpMulticastSender {

    private final String host; // 组播地址
    private final int port;    // 组播端口
    private Channel channel;   // Netty 通道
    private NioEventLoopGroup group; // 线程组

    public UdpMulticastSender(String host, int port) {
        this.host = host;
        this.port = port;
    }

    /**
     * 初始化并启动 Netty 通道
     *
     * @return 返回 Netty 通道，供外部发送数据
     * @throws InterruptedException 如果启动失败
     */
    public Channel start() throws InterruptedException {
        group = new NioEventLoopGroup(); // 初始化线程组
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(group)
                .channel(NioDatagramChannel.class)
                .handler(new ChannelInitializer<NioDatagramChannel>() {
                    @Override
                    protected void initChannel(NioDatagramChannel ch) {
                        // 无需额外 Handler
                    }
                });

        // 绑定到任意可用端口
        ChannelFuture future = bootstrap.bind(0).sync();
        channel = future.channel();
        log.info("UDP Multicast Sender started on host: {}, port: {}", host, port);
        return channel;
    }

    /**
     * 发送数据到组播地址
     *
     * @param message 要发送的消息
     * @throws InterruptedException 如果发送失败
     */
    public void sendMessage(String message) {
        sendMessage(message.getBytes());
        log.info("Message sent: {}", message);
    }

    public void sendMessage(byte[] bytes) {
        sendMessage(Unpooled.copiedBuffer(bytes));
        log.info("Message sent");
    }

    public void sendMessage(ByteBuf byteBuf) {
        if (channel == null) {
            throw new IllegalStateException("Sender is not started. Call start() first.");
        }

        try {
            DatagramPacket packet = new DatagramPacket(
                    byteBuf, // 消息数据
                    new InetSocketAddress(host, port)          // 目标组播地址和端口
            );
            channel.writeAndFlush(packet).sync();
            log.info("Message sent success");
        } catch (Exception e) {
            log.error("消息发送失败，原因", e);
        }
    }

    /**
     * 停止发送端并释放资源
     */
    public void stop() {
        if (channel != null) {
            channel.close();
        }
        if (group != null) {
            group.shutdownGracefully();
        }
        log.info("UDP Multicast Sender stopped.");
    }
}
