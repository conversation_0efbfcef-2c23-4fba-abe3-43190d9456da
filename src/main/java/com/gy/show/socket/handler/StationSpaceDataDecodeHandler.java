package com.gy.show.socket.handler;

import com.alibaba.fastjson.JSON;
import com.gy.show.controller.external.InteractionLogDTO;
import com.gy.show.service.StationDataService;
import com.gy.show.socket.message.StationSpaceDataHead;
import com.gy.show.ws.FullViewTsServer;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@ChannelHandler.Sharable
@Slf4j
@Component
public class StationSpaceDataDecodeHandler extends ChannelInboundHandlerAdapter {

    private static StationDataService stationDataService;

    private static FullViewTsServer server;

    @Resource
    public void setServer(FullViewTsServer server) {
        StationSpaceDataDecodeHandler.server = server;
    }

    @Resource
    public void setExternalDataService(StationDataService stationDataService) {
        StationSpaceDataDecodeHandler.stationDataService = stationDataService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        NioDatagramChannel channel = (NioDatagramChannel) ctx.channel();
        log.info("链接报告信息：本客户端链接到服务端。channelId：{}, IP: {}, PORT:{}", channel.id(), channel.localAddress().getHostString(), channel.localAddress().getPort());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.info("断开链接,{}", ctx.channel().localAddress().toString());
        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        String id = ctx.channel().attr(AttributeKey.valueOf("id")).get().toString();
        log.info("接收到航天测控站:{} 遥测数据包，开始读取数据", id);

        // 解析报文头
        StationSpaceDataHead head = stationDataService.parseMessageHead(msg);
        log.info("解析头部信息结果：{}", head);

        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        ctx.channel().attr(AttributeKey.valueOf("source")).set("航天测控站" + id);

//        server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收到航天测控站：" + id +"遥测信息, 数据包长度：" + byteBuf.readableBytes())));

        try {
            // 解析报文体
            stationDataService.parseMessageBody(msg, head, id);
        } finally {
            byteBuf.release();
        }

    }

}
