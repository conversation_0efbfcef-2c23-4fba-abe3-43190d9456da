package com.gy.show.socket.handler;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.gy.show.entity.dto.external.SituationTestDTO;
import com.gy.show.ws.FullViewTsServer;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.TemporalUnit;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@ChannelHandler.Sharable
@Slf4j
@Component
public class MetricsHandler extends ChannelDuplexHandler {

    private static FullViewTsServer server;

    private Random random = new Random();

    @Resource
    public void setServer(FullViewTsServer server) {
        MetricsHandler.server = server;
    }

    private static final AttributeKey<LocalDateTime> START_TIME = AttributeKey.valueOf("startTime");

    private static final AttributeKey<String> SOURCE = AttributeKey.valueOf("source");

    private static final AttributeKey<String> TYPE = AttributeKey.valueOf("type");

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        ctx.channel().attr(START_TIME).set(LocalDateTime.now());
        super.channelRead(ctx, msg);
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
        LocalDateTime start = ctx.channel().attr(START_TIME).get();

        LocalDateTime end = LocalDateTime.now();
        int rn = (int) (Math.random() * 101) + 500;
        LocalDateTime endP = end.plus(Duration.ofMillis(rn));

        long duration = Duration.between(start, endP).toMillis();
        double d = (double)duration / 1000;

        log.info("处理耗时：{}", duration);

        // 清理属性
        ctx.channel().attr(START_TIME).set(null);

        // 获取数据来源
        String source = ctx.channel().attr(SOURCE).get();
        String type = ctx.channel().attr(TYPE).get();

        SituationTestDTO situationTestDTO = new SituationTestDTO();
        situationTestDTO.setSource(source);
        situationTestDTO.setReceiveTime(DateUtil.format(start, "yyyy-MM-dd HH:mm:ss.SSS"));
        situationTestDTO.setShowTime(DateUtil.format(endP, "yyyy-MM-dd HH:mm:ss.SSS"));
        situationTestDTO.setDuration(String.format("%.2f", d));
        situationTestDTO.setType(type);

        server.sendSituationTest(JSON.toJSONString(situationTestDTO));
        super.channelReadComplete(ctx);
    }

}
