package com.gy.show.socket.handler;

import com.alibaba.fastjson.JSON;
import com.gy.show.controller.external.InteractionLogDTO;
import com.gy.show.service.StationSpaceStatusService;
import com.gy.show.ws.FullViewTsServer;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@ChannelHandler.Sharable
@Slf4j
@Component
public class StationSpaceStatusDecodeHandler extends ChannelInboundHandlerAdapter {

    private static StationSpaceStatusService stationSpaceStatusService;

    private static FullViewTsServer server;

    @Resource
    public void setServer(FullViewTsServer server) {
        StationSpaceStatusDecodeHandler.server = server;
    }

    @Resource
    public void setExternalDataService(StationSpaceStatusService stationSpaceStatusService) {
        StationSpaceStatusDecodeHandler.stationSpaceStatusService = stationSpaceStatusService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        NioDatagramChannel channel = (NioDatagramChannel) ctx.channel();
        log.info("链接报告信息：本客户端链接到服务端。channelId：{}, IP: {}, PORT:{}", channel.id(), channel.localAddress().getHostString(), channel.localAddress().getPort());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.info("断开链接,{}", ctx.channel().localAddress().toString());
        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        String id = ctx.channel().attr(AttributeKey.valueOf("id")).get().toString();
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        ctx.channel().attr(AttributeKey.valueOf("source")).set(id.equalsIgnoreCase("_ka") ? "天基综合节点" : "空基综合节点");
        ctx.channel().attr(AttributeKey.valueOf("type")).set("综合节点状态信息");

        log.info("接收到航天测控站：{} 数据信息，开始读取数据,数据包长度：{}", id, byteBuf.readableBytes());
//        server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收到航天测控站：" + id +"状态信息, 数据包长度：" + byteBuf.readableBytes())));


        try {
            stationSpaceStatusService.parseSpaceMessage(byteBuf, id);
        } finally {
            byteBuf.release();
        }
    }


}
