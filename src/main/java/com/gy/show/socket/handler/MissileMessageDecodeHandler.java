package com.gy.show.socket.handler;

import com.alibaba.fastjson.JSON;
import com.gy.show.controller.external.InteractionLogDTO;
import com.gy.show.service.MissileService;
import com.gy.show.ws.FullViewTsServer;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@ChannelHandler.Sharable
@Slf4j
@Component
public class MissileMessageDecodeHandler extends ChannelInboundHandlerAdapter {

    private static MissileService missileService;

    private static FullViewTsServer server;

    @Resource
    public void setServer(FullViewTsServer server) {
        MissileMessageDecodeHandler.server = server;
    }

    @Resource
    public void setExternalDataService(MissileService missileService) {
        MissileMessageDecodeHandler.missileService = missileService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        NioDatagramChannel channel = (NioDatagramChannel) ctx.channel();
        log.info("导弹链接报告信息：本客户端链接到服务端。channelId：{}, IP: {}, PORT:{}", channel.id(), channel.localAddress().getHostString(), channel.localAddress().getPort());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.info("导弹断开链接,{}", ctx.channel().localAddress().toString());
        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        String id = ctx.channel().attr(AttributeKey.valueOf("id")).get().toString();

        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        log.info("接收导弹测控站 :{} 发送的数据包，开始读取数据", id);
        server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收导弹测控站：" + id +"状态信息, 数据包长度：" + byteBuf.readableBytes())));

        try {
            missileService.parseMessage(byteBuf, id);
        } finally {
            byteBuf.release();
        }

    }

}
