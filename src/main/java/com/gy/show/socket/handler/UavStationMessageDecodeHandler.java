package com.gy.show.socket.handler;

import com.gy.show.service.UavStationService;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@ChannelHandler.Sharable
@Slf4j
@Component
public class UavStationMessageDecodeHandler extends ChannelInboundHandlerAdapter {

    private static UavStationService uavStationService;

    @Resource
    public void setExternalDataService(UavStationService uavStationService) {
        UavStationMessageDecodeHandler.uavStationService = uavStationService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        NioDatagramChannel channel = (NioDatagramChannel) ctx.channel();
        log.info("无人机测控站链接报告信息：本客户端链接到服务端。channelId：{}, IP: {}, PORT:{}", channel.id(), channel.localAddress().getHostString(), channel.localAddress().getPort());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.info("无人机测控站断开链接,{}", ctx.channel().localAddress().toString());
        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        log.info("接收无人机测控站发送的数据包，开始读取数据");
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();
        String id = ctx.channel().attr(AttributeKey.valueOf("id")).get().toString();

        try {
            uavStationService.parseMessage(byteBuf, id);
        } finally {
            byteBuf.release();
        }

    }

}
