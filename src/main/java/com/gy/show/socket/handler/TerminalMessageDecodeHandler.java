package com.gy.show.socket.handler;

import com.gy.show.service.TerminalDataService;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@ChannelHandler.Sharable
@Slf4j
@Component
public class TerminalMessageDecodeHandler extends ChannelInboundHandlerAdapter {

    private static TerminalDataService terminalDataService;

    @Resource
    public void setExternalDataService(TerminalDataService terminalDataService) {
        TerminalMessageDecodeHandler.terminalDataService = terminalDataService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        NioDatagramChannel channel = (NioDatagramChannel) ctx.channel();
        log.info("链接报告信息：本客户端链接到服务端。channelId：{}, IP: {}, PORT:{}", channel.id(), channel.localAddress().getHostString(), channel.localAddress().getPort());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.info("断开链接,{}", ctx.channel().localAddress().toString());
        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        log.info("接收终端发送的数据包，开始读取数据");
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        // 解析报文头
        try {
            terminalDataService.parseTerminalMessage(ctx, byteBuf);
        } finally {
            byteBuf.release();
        }
    }

}
