package com.gy.show.socket;

import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.socket.message.fields.UnSignedByteField;
import com.gy.show.socket.message.fields.DateField;
import com.gy.show.socket.message.fields.LongField;
import com.gy.show.socket.message.fields.ShortField;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramChannel;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
public class UdpSender {

    private Boolean changed = true;

    public Boolean getChanged() {
        return changed;
    }

    public void setChanged(Boolean changed) {
        this.changed = changed;
    }

    public void start() throws InterruptedException {
        // 配置 Netty 的 Bootstrap
        NioEventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(group)
                    .channel(NioDatagramChannel.class)
                    .handler(new ChannelInitializer<DatagramChannel>() {
                        @Override
                        protected void initChannel(DatagramChannel datagramChannel) {
                            // 空实现，因为不需要额外的处理器
                        }
                    });

            Channel channel = bootstrap.bind(0).sync().channel();

            // 构造二进制数据
            packageRequirementData(channel);
//            while (true) {
//                packageData(channel);
//
//                Thread.sleep(2000);
//            }


        } finally {
            group.shutdownGracefully();
        }
    }

    private static void packageSpaceStationData(Channel channel) {
        ByteBuf byteBuf = Unpooled.buffer();

        // head
        byteBuf.writeBytes(new byte[8]);

        // 信息类别 4
        byteBuf.writeIntLE(61700);

        byteBuf.writeBytes(new byte[16]);

    }

    private static void packageRequirementData(Channel channel) throws InterruptedException {
        ByteBuf byteBuf = Unpooled.buffer();

        // head
        byteBuf.writeShort(1);
        byteBuf.writeShort(123);
        byteBuf.writeShort(8000);
        byteBuf.writeByte(0x02);
        byteBuf.writeShort(60);
        byteBuf.writeByte(1);
        byteBuf.writeByte(1);
        byteBuf.writeBytes(new byte[37]);

        // 接口类型 1
        byte type = (byte) Integer.parseInt("00", 16);
        UnSignedByteField.writeField(byteBuf, type);
        // 需求总数 2
        ShortField.writeField(byteBuf, 1);
        // 需求序号 2
        ShortField.writeField(byteBuf, "1111");
        // 需求名称 20
        byte[] name = new String("导弹打击任务").getBytes(Charset.forName("UTF-8"));

        byte[] bytes = new byte[20];
        if (name.length > 20) {
            System.arraycopy(name, 0, bytes, 0, 20);
        } else {
            System.arraycopy(name, 0, bytes, 0, name.length);
        }
        byteBuf.writeBytes(bytes);
        // 需求类型 1
        byteBuf.writeByte(1);
        // 重要程度 1
        byteBuf.writeByte(1);
        // 需求开始时间 7
        DateField.writeField(byteBuf, LocalDateTime.of(2025, 7, 1, 8, 0));
        // 需求结束时间 7
        DateField.writeField(byteBuf, LocalDateTime.of(2025, 7, 1, 18, 0));
        // 需求描述 60
        byteBuf.writeBytes(new byte[60]);

        // 目标数量 2
        byteBuf.writeShort(1);


        List<RequirementInfoDTO.TargetInfo> targetInfos = new ArrayList<>();
        for (int i = 0; i < 1; i++) {
            // 目标序号 2
            byteBuf.writeShort(i);
            // 目标ID 8
            byteBuf.writeLong(1813086279399944194L);
            // 目标类型 1
            byteBuf.writeByte(1);
            // 所属域 1
            byteBuf.writeByte(1);
            // 轨迹开始时间 7
            DateField.writeField(byteBuf, LocalDateTime.of(2025, 7, 1, 9, 0));
            // 轨迹结束时间 7
            DateField.writeField(byteBuf, LocalDateTime.of(2025, 7, 1, 10, 0));

            // 轨迹名称 20
            byteBuf.writeBytes(new byte[20]);
            // 坐标点数 4
            byteBuf.writeInt(2);

            /** 解析坐标点 start **/
            for (int j = 0; j < 2; j++) {
                // 坐标序号 4
                byteBuf.writeInt(j);
                // 经度 4
                byteBuf.writeInt(0x45970343);

                // 纬度 4 (稍作变化)
                byteBuf.writeInt(0x765421);

                // 高度 4
                byteBuf.writeInt(0x6542);
                // 速度 4
                byteBuf.writeInt(0x12765421);
                // 预留 4
                byteBuf.writeBytes(new byte[4]);
            }

            byteBuf.writeShort(1);

            for (int j = 0; j < 1; j++) {
                // 任务ID 2
                byteBuf.writeShort(1);

                // TODO 任务类型 1
                byteBuf.writeByte(10);

                // 任务开始时间 7
                DateField.writeField(byteBuf, LocalDateTime.of(2025, 7, 1, 9, 0));

                // 任务结束时间 7
                DateField.writeField(byteBuf, LocalDateTime.of(2025, 7, 1, 10, 0));

                // 周期性 1
                byteBuf.writeByte(0);

                // 预留 4
                byteBuf.writeBytes(new byte[4]);

            }
        }

        // 发送数据
        InetSocketAddress targetAddress = new InetSocketAddress("127.0.0.1", 8000); // 目标地址和端口
        DatagramPacket packet = new DatagramPacket(byteBuf, targetAddress);
        ChannelFuture future = channel.writeAndFlush(packet).sync();

        if (future.isSuccess()) {
            System.out.println("数据发送成功！");
        } else {
            System.err.println("数据发送失败：" + future.cause());
        }
    }

    private void packageData(Channel channel) throws InterruptedException {
        ByteBuf byteBuf = Unpooled.buffer();

        // head
        byteBuf.writeShort(1);
        byteBuf.writeShort(123);
        byteBuf.writeShort(8000);
        byteBuf.writeByte(0x02);
        byteBuf.writeShort(60);
        byteBuf.writeByte(1);
        byteBuf.writeByte(1);
        byteBuf.writeBytes(new byte[37]);

        // body
        // 接口类型 1
        byteBuf.writeByte(2);

        // 目标数量 1
        int targetCount = 1; // 示例为 2 个目标
        byteBuf.writeByte(targetCount);

        for (int i = 0; i < targetCount; i++) {

            // 目标序号 1
            byteBuf.writeByte(i);

            // 目标ID 2
            byteBuf.writeLong(1837786164791087105L);

            // 目标名称 20
            String name = "WZ5603-2";
            byte[] bytes = name.getBytes(StandardCharsets.UTF_8);
            byteBuf.writeBytes(bytes);
            if (bytes.length < 20) {
                byteBuf.writeZero(20 - bytes.length);
            }

            // 目标类别
            byteBuf.writeByte(0x00);

            // 俯仰角 1
            byteBuf.writeByte(45);

            // 侧倾角 1
            byteBuf.writeByte(30);

            // 航向角 2
            byteBuf.writeShort(90);

            // 经度 4 (稍作变化)
            byteBuf.writeInt(changed ? 0x45970343 : 0x45983212);

            // 纬度 4 (稍作变化)
            byteBuf.writeInt(changed ? 0x12765421 : 0x12448213);

            // 跟踪偏差 2

            byteBuf.writeShort(5);

            // 速度 4
            byteBuf.writeInt(60);

            // 目标跟踪状态 1
            byteBuf.writeByte(2);

            // 单次里程 4
            byteBuf.writeInt(1000);

            // 超声波检测距离 4
            byteBuf.writeInt(300);

            // 平台机动状态 1
            byteBuf.writeByte(0b10101011);

            // 车速 2
            byteBuf.writeShort(80);

            // 系统电压（电池状态电压V） 1
            byteBuf.writeByte(12);

            // 电池Soc 1
            byteBuf.writeByte(80);

            // 电池电流 2
            byteBuf.writeShort(3);

            // 发电机电流 1
            byteBuf.writeByte(5);

            // 发动机转速 1
            byteBuf.writeByte(1200);

            // 地盘状态 1
            byteBuf.writeByte(0);

            // 底盘各部分状态 1
            byteBuf.writeByte(changed ? 0b00000000 : 0b00000000);

            // 油量 1
            byteBuf.writeByte(50);

            byteBuf.writeByte(1);

            byteBuf.writeByte(1);

            // 执行任务数量 2
            byteBuf.writeShort(1);

            // 执行任务序号 2
            byteBuf.writeShort(1);

            // 需求ID 8
            byteBuf.writeLong(111);

            // 任务ID 8
            byteBuf.writeLong(222);

            // 接入节点代号 8
            byteBuf.writeBytes(new byte[20]);

            // 任务执行进度 2
            byteBuf.writeShort(123);

            // 预留 16
            byteBuf.writeBytes(new byte[16]);
        }

        byteBuf.writeByte(1);

        // 发送数据
        InetSocketAddress targetAddress = new InetSocketAddress("127.0.0.1", 8000); // 目标地址和端口
        DatagramPacket packet = new DatagramPacket(byteBuf, targetAddress);
        ChannelFuture future = channel.writeAndFlush(packet).sync();

        if (future.isSuccess()) {
            System.out.println("数据发送成功！");
        } else {
            System.err.println("数据发送失败：" + future.cause());
        }

    }
}

