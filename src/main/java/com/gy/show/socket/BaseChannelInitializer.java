package com.gy.show.socket;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;

public class BaseChannelInitializer extends ChannelInitializer<SocketChannel> {

    @Override
    protected void initChannel(SocketChannel channel) throws Exception {
        //对象传输处理
        // 流量整形控制器
//        channel.pipeline().addLast(new FlowMonitoringHandler(1*1024*1024L,1*1024*1024L,1000L));
        }
}
