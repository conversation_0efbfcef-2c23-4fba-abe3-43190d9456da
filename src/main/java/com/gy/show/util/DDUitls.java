package com.gy.show.util;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

/*
* DD 工具类
* */
public class DDUitls {

    // 地球半径（千米）
    public  static double arc = 6371.393;

    /**
     * 根据一点经纬度，到另一点距离，方位值，计算另一点的经纬度
     * @param lon1
     * @param lat1
     * @param bearing
     * @param distance
     * @return
     */
    public static double[] LongLatOffset(double lon1, double lat1, double bearing, double distance){
        // 方位角 角度转化为弧度
        lon1 = Math.toRadians(lon1);
        lat1 = Math.toRadians(lat1);
        bearing = Math.toRadians(bearing);
        double lat2 = Math.asin(Math.sin(lat1) * Math.cos(distance / arc) +
                Math.cos(lat1) * Math.sin(distance / arc) * Math.cos(bearing));

        double lon2 = lon1 + Math.atan2(Math.sin(bearing) * Math.sin(distance / arc) * Math.cos(lat1),
                Math.cos(distance / arc) - Math.sin(lat1) * Math.sin(lat2));
        return new double[]{canvert(Math.toDegrees(lon2)), canvert(Math.toDegrees(lat2))};
    }


    /**
     * 求坐标2相对于坐标1的方位角度和距离
     * @param lon1
     * @param lat1
     * @param lon2
     * @param lat2
     * @return
     */
    public static double[] bearingDistanceOffset(double lon1, double lat1, double lon2, double lat2){
        lon1 = Math.toRadians(lon1);
        lat1 = Math.toRadians(lat1);
        lon2 = Math.toRadians(lon2);
        lat2 = Math.toRadians(lat2);

        // 方位角计算
        double bearing = Math.atan2(Math.sin(lon2 - lon1) * Math.cos(lat2),
                Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 -lon1));
        // 距离计算
        double distance = arc * Math.acos(Math.sin(lat1) * Math.sin(lat2) + Math.cos(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1));
        return new double[]{canvert(Math.toDegrees(bearing)) +180 ,canvert(distance)};
    }


    /**
     *  根据高度差和距离计算
     * @param height
     * @param distance
     * @return
     */
    public static double pitching(double height, double distance){
        return canvert(Math.toDegrees(Math.atan2(height, distance)));
    }

    /**
     *  根据俯仰角，高度，距离求目标高度
     * @param fyz
     * @param height
     * @return
     */
    public static double getHeight(double fyz, double height, double distance){
        return canvert(Math.tan(Math.toRadians(fyz)) * distance + height);
    }

    /**
     * Double 数据格式化（保留6位小数）
     * @param d
     * @return
     */
    public static double canvert(double d){
        BigDecimal decimal = new BigDecimal(d);
        return decimal.setScale(6,BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    public static void main(String[] args) {
//        double[] d = bearingDistanceOffset(95.03,15.72,95.317, 15.924);
//        System.out.println(d[0] + "-------" + d[1]);
//        double[] f =  LongLatOffset(100,30,d[0],d[1]);
//        System.out.println(f[0] + "--------" + f[1]);
//
//
//        List<Integer> newEquipmentIds =StringUtil.stringList2IntList(Arrays.asList("1,2,3".split(",")));
//        System.out.println(newEquipmentIds);
//
//        int id = (int)(Math.random() * 100000000 + 1 );
        System.out.println(LocalTime.now());
    }
}
