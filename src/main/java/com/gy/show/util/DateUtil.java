package com.gy.show.util;

import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

@Data
@Configuration
public class DateUtil {

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");
    private static SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static SimpleDateFormat sdftime = new SimpleDateFormat("yyyyMMddhhmmssss");

    private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static Instant toInstant(LocalDateTime time) {
        ZonedDateTime zonedDateTime = time.atZone(ZoneId.systemDefault());
        return zonedDateTime.toInstant();
    }

    public static String getTimestrap() {
        return sdf.format(new Date());
    }

    public static String getTimestrapMil() {
        return sdftime.format(new Date());
    }

    public static String ctDateStr(Date date) {
        return sdf1.format(date);
    }

    public static String datetime(Date date) {
        return sdftime.format(date);
    }

    public static String datetimes(Date date) {
        return sdf1.format(date);
    }

    private static DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static String getDTF(LocalDateTime localDateTime) {
        return dtf.format(localDateTime);
    }


    public static Long getLongTime(String date) throws ParseException {
        Date date1 = sdf.parse(date);
        return date1.getTime();
    }

    public static Date datetime(String date) {
        try {
            return sdf1.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date datetimeNoLine(String date) {
        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    String key="satellite";

    String filed="name";

    /**
     * 
     * @param date
     * @param i
     * @param d
     * @return
     */
    public static Date dateRoll(Date date,int i,int d){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(i,d);
        return calendar.getTime();
    }

    public static long daysSince2000() {
        LocalDate sinceDate = LocalDate.of(2000, 1, 1);
        LocalDate now = LocalDate.now();

        return ChronoUnit.DAYS.between(sinceDate, now);
    }

    public static long getDaySeconds() {
        LocalTime since = LocalTime.of(0, 0, 0);
        LocalTime now = LocalTime.now();

        Duration duration = Duration.between(since, now);

        return duration.toMillis() * 10;
    }

}
