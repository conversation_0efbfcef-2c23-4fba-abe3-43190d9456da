package com.gy.show.util;


import com.alibaba.fastjson.JSONArray;
import com.cetc10.spaceflight.orbitpre.orbit.LLAPredict;
import com.cetc10.spaceflight.orbitpre.orbit.OrbitProcTLE;
import com.cetc10.spaceflight.orbitpre.orbit.Tle;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dto.CoordinateWithTimeDTO;
import com.vividsolutions.jts.util.GeometricShapeFactory;
import org.apache.commons.lang3.StringUtils;
import org.geotools.referencing.GeodeticCalculator;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.locationtech.jts.geom.*;
import org.opengis.referencing.crs.CoordinateReferenceSystem;

import java.awt.geom.GeneralPath;
import java.awt.geom.Point2D;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

public class GISUtils {

    /**
     * 判断航迹是否在多边形区域内
     *
     * @param track
     * @param polygon
     * @return
     */
    public static boolean isTrackInPolygon(LineString track, Polygon polygon) {
        Geometry intersection = track.intersection(polygon);
        return !intersection.isEmpty();
    }

    public static Instant interpolateTime(CoordinateWithTimeDTO[] path, Coordinate coord) {
        for (int i = 1; i < path.length; i++) {
            CoordinateWithTimeDTO prev = path[i - 1];
            CoordinateWithTimeDTO next = path[i];
            if (isBetween(prev.getCoordinate(), next.getCoordinate(), coord)) {
                double totalDistance = prev.getCoordinate().distance(next.getCoordinate());
                double entryDistance = prev.getCoordinate().distance(coord);
                double fraction = entryDistance / totalDistance;
                long timeDiff = next.getTime().toEpochMilli() - prev.getTime().toEpochMilli();
                return Instant.ofEpochMilli(prev.getTime().toEpochMilli() + Math.round(fraction * timeDiff));
            }
        }
        return null;
//        throw new IllegalArgumentException("Coordinate is not within the flight path");
    }

    private static boolean isBetween(Coordinate start, Coordinate end, Coordinate target) {
        double crossProduct = (target.y - start.y) * (end.x - start.x) - (target.x - start.x) * (end.y - start.y);
        if (Math.abs(crossProduct) > 1e-6) return false;

        double dotProduct = (target.x - start.x) * (end.x - start.x) + (target.y - start.y) * (end.y - start.y);
        if (dotProduct < 0) return false;

        double squaredLength = (end.x - start.x) * (end.x - start.x) + (end.y - start.y) * (end.y - start.y);
        return dotProduct <= squaredLength;
    }

    // 将角度转换为弧度
    public static double degToRad(double deg) {
        return deg * Math.PI / 180.0;
    }

    // 地球半径，单位：公里
    private static final double R = 6371.0;

    // 计算两个坐标点之间的距离，单位：公里
    public static double calculateDistance(double latA, double lonA, double latB, double lonB) {
        // 转换为弧度
        double latARad = degToRad(latA);
        double lonARad = degToRad(lonA);
        double latBRad = degToRad(latB);
        double lonBRad = degToRad(lonB);

        // 计算经纬度差值
        double deltaLat = latBRad - latARad;
        double deltaLon = lonBRad - lonARad;

        // Haversine 公式
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                Math.cos(latARad) * Math.cos(latBRad) * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        // 计算距离，单位：公里
        return R * c;
    }

    public static double calculateProjectionRadius(double height, double theta) {
        // 将角度转换为弧度
        double thetaRad = Math.toRadians(theta);

        // 计算圆锥底部的半径
        double radius = height * Math.tan(thetaRad / 2);

        return radius;

        // 将经纬度转换为笛卡尔坐标
//        double[] cartesian = latLonToCartesian(latitude, longitude);
//
//        // 生成圆形多边形
//        GeometryFactory geometryFactory = new GeometryFactory();
//        Point center = geometryFactory.createPoint(new Coordinate(cartesian[0], cartesian[1]));
//        Polygon coneBase = createCircle(center, radius, geometryFactory);
//
//        // 计算面积
//        return coneBase.getArea();
    }

    public static double[] latLonToCartesian(double lat, double lon) {
        double x = EARTH_RADIUS * Math.toRadians(lon);
        double y = EARTH_RADIUS * Math.toRadians(lat);
        return new double[]{x, y};
    }

    /**
     * 创建一个圆形区域
     *
     * @param center
     * @param radius
     * @param factory
     * @return
     */
    public static Polygon createCircle(Coordinate center, double radius, GeometryFactory factory) {
        // 将半径从米转换为度
//        double radiusInDegrees = radius / (EARTH_RADIUS * Math.cos(Math.toRadians(center.y)));

        double radiusInDegrees0 = radius / METERS_PER_DEGREE;

        Point point = factory.createPoint(center);
        // 使用 buffer 方法创建圆形多边形
        return (Polygon) point.buffer(radiusInDegrees0);
    }

//    public static Polygon createCircle0(Coordinate center, double radius, GeometryFactory factory) throws Exception {
//        // 创建 WGS84 坐标参考系统
//        CoordinateReferenceSystem crs = CRS.decode("EPSG:4326");
//        double radiusInDegrees = radius / EARTH_RADIUS * (180 / Math.PI);
//
//        Point point = factory.createPoint(center);
//
//        // 将点缓冲生成圆形区域
//        MathTransform transform = CRS.findMathTransform(crs, crs);
//        Geometry buffer = point.buffer(radiusInDegrees);
//        JTS
//        Polygon circle = (Polygon) JTS.transform(buffer, transform);
//        // 使用 buffer 方法创建圆形多边形
//        return (Polygon) point.buffer(radiusInDegrees0);
//    }

    /**
     * 计算航迹在区域内的覆盖率
     *
     * @param track
     * @param polygon
     * @return
     */
    public static double calculateCoverageRate(LineString track, Polygon polygon) {
        // 计算航迹总长度
        double total = track.getLength();

        // 计算航迹与区域的交集
        Geometry intersection = track.intersection(polygon);
        double intersectionLength = intersection.getLength();
        return intersectionLength / total;
    }

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    // private static final Logger LOGGER = LoggerFactory.getLogger(TsWebSocketClientServiceServiceImpl.class);
    // 地球半径
    private static double EARTH_RADIUS = 6378137;

    private static double METERS_PER_DEGREE = 111320.0;


    /**
     * 通过经纬度获取距离(单位：米) lat 经度 lng 纬度
     *
     * @param source 源
     * @param target 目标
     * @return
     */
    public static double getDistance(Point2D.Double source, Point2D.Double target) {
        double radLat1 = rad(source.x);
        double radLat2 = rad(target.x);
        double a = radLat1 - radLat2;
        double b = rad(source.y) - rad(target.y);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = Math.round(s * 10000d) / 10000d;
        return s;
    }

    public static double getDistanceNew(Point2D.Double source, Point2D.Double target) {
        double lat1 = Math.toRadians(source.y);
        double lat2 = Math.toRadians(target.y);
        double lon1 = Math.toRadians(source.x);
        double lon2 = Math.toRadians(target.x);
        double a = lat1 - lat2;
        double b = lon1 - lon2;
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        return s;
    }

    /**
     * 解析字符串为Point2D.Double对象
     *
     * @param lng
     * @param lat
     * @return
     */
    public static Point2D.Double parsePoint2DDouble(String lng, String lat) {
        if (Objects.isNull(lng) || Objects.isNull(lat)) {
            return null;
        }
        if (StringUtils.isEmpty(lng) && StringUtils.isEmpty(lat))
            throw new ServiceException("经纬度为空");
        return new Point2D.Double(Double.parseDouble(lng), Double.parseDouble(lat));
    }

    /**
     * 解析JSONArray为Point2D.Double数组
     *
     * @param pointArray
     * @return
     */
    public static List<Point2D.Double> parsePoint2DDoubleArray(JSONArray pointArray) {
        if (Objects.isNull(pointArray)) {
            //LOGGER.error("图形JSON数组为空");
            throw new ServiceException("图形JSON数组为空");
        } else {
            List<Point2D.Double> array = new ArrayList();
            Iterator<Object> iterator = pointArray.iterator();
            while (iterator.hasNext()) {
                JSONArray next = (JSONArray) iterator.next();
                array.add(parsePoint2DDouble(next.getString(0), next.getString(1)));
            }
            return array;
        }
    }

    /**
     * 判断点是否在区域内
     *
     * @param point   要判断得到点的横纵坐标
     * @param polygon 多边形组成定点的坐标集合
     * @return
     */
    public static boolean isInPolygon(Point2D.Double point, List<Point2D.Double> polygon) {
        GeneralPath peneralPath = new GeneralPath();
        Point2D.Double first = polygon.get(0);
        // 通过移动到制定坐标 添加一个点到路径中
        peneralPath.moveTo(polygon.get(0).x, polygon.get(0).y);
        polygon.remove(0);
        //通过绘制一条从当前坐标到新制定坐标的直线, 将一个点添加到路径中.
        for (Point2D.Double d : polygon) peneralPath.lineTo(d.x, d.y);
        //将集合多边形封闭
        peneralPath.lineTo(first.x, first.y);
        peneralPath.closePath();
        //测试制定的Point2D是否在Shape的便捷
        return peneralPath.contains(point);
    }

    /**
     * 点是否在圆内
     *
     * @param source 点坐标
     * @param target 圆坐标
     * @param radius 半径
     * @return
     */
    public static boolean isInCircle(Point2D.Double source, Point2D.Double target, Double radius) {
        return getDistanceNew(source, target) < radius;
    }

    /**
     * 判断是否在经纬范围内
     *
     * @param point
     * @param left
     * @param right
     * @return
     */
    public static boolean isInRange(double point, double left, double right) {
        return point >= Math.min(left, right) && point <= Math.max(left, right);
    }

    /**
     * 是否在矩形区域内
     *
     * @param point       点
     * @param leftTop     矩形坐上
     * @param rightBottom 矩形右下
     * @return
     */
    public static boolean isInRectangle(Point2D.Double point, Point2D.Double leftTop, Point2D.Double rightBottom) {
        if (Objects.isNull(point) || Objects.isNull(leftTop) || Objects.isNull(rightBottom)) {
            return false;
        }
        Double lng = point.x;                       // 测试点经度
        Double lat = point.y;                       // 测试点纬度
        Double minLng = leftTop.x;                  // 经度限制范围1
        Double maxLng = rightBottom.x;              // 经度范围限制2
        Double minLat = rightBottom.y;              // 纬度范围限制1
        Double maxLat = leftTop.y;                  // 纬度范围限制2
        Double left = Math.max(minLng, maxLng);     //
        Double right = Math.min(minLng, maxLng);    //
        return isInRange(lat, minLat, maxLat)
                ? minLng * maxLng > 0
                ? isInRange(lng, minLng, maxLng)
                : Math.abs(minLng) + Math.abs(maxLng) < 180
                ? isInRange(lng, minLng, maxLng)
                : isInRange(lng, left, 180) || isInRange(lng, right, -180)
                : false;
    }

    /**
     * 目标轨迹算法
     *
     * @param directData 根数
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return
     */
    public static List<LLAPredict> getLlaPredicts(List<String> directData, LocalDateTime startTime, LocalDateTime endTime, double step) {
        startTime = startTime.plusHours(8);
        endTime = endTime.plusHours(8);
        //20230820
        if (directData.size() != 3)
            return new ArrayList<>();
        OrbitProcTLE orbitProcTLE = new OrbitProcTLE(new Tle(directData.get(0), directData.get(1), directData.get(2)));

        // 减8小时
        return orbitProcTLE.calcLLA(
                Date.from(startTime.minusHours(8).atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(endTime.minusHours(8).atZone(ZoneId.systemDefault()).toInstant()), step);
    }


    /**
     * 插入点
     * 内部计算距离
     *
     * @return
     */
    public static List<Point2D.Double> insertPoint(List<Point2D.Double> datas, int speed) {
        List<Point2D.Double> points = new ArrayList<>();
        double lon1 = 0.0;
        double lat1 = 0.0;
        double lon2 = 0.0;
        double lat2 = 0.0;
        for (int i = 1; i < datas.size(); i++) {
            lon1 = datas.get(i - 1).getX();
            lat1 = datas.get(i - 1).getY();
            lon2 = datas.get(i).getX();
            lat2 = datas.get(i).getY();
            //计算两点间的距离
            double lonLen = lon2 - lon1;
            double latLen = lat2 - lat1;
            double distance = getDistanceNew(datas.get(i - 1), datas.get(i));
            int ceil = (int) Math.ceil(distance / speed);
            System.err.println(ceil);
            double lon = 0;
            double lat = 0;
            double lonCeil = lonLen / ceil;
            double latCeil = latLen / ceil;
            for (int j = 0; j < ceil + 1; j++) {
                lon = lon1 + lonCeil * j;
                lat = lat1 + latCeil * j;
                points.add(new Point2D.Double(lon, lat));
            }
        }
        return points;
    }

    // 度数转弧度
    private static double toRadians(double degree) {
        return degree * Math.PI / 180.0;
    }

    // 弧度转度数
    private static double toDegrees(double radian) {
        return radian * 180.0 / Math.PI;
    }

    // 计算大圆距离（单位为米）
    private static double calculateGreatCircleDistance(double lat1, double lon1, double lat2, double lon2) {
        // 使用球面余弦公式计算距离
        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;

        double a = Math.pow(Math.sin(dLat / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(dLon / 2), 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c; // 距离为米
    }

    // 计算中间点的经纬度
    public static double[] calculateIntermediatePoint(double lat1, double lon1, double lat2, double lon2, double fraction) {
        // 将经纬度转换为弧度
        lat1 = toRadians(lat1);
        lon1 = toRadians(lon1);
        lat2 = toRadians(lat2);
        lon2 = toRadians(lon2);

        // 计算大圆距离
        double distance = calculateGreatCircleDistance(lat1, lon1, lat2, lon2);

        // 计算中间点的纬度
        double A = Math.sin((1 - fraction) * distance / EARTH_RADIUS) / Math.sin(distance / EARTH_RADIUS);
        double B = Math.sin(fraction * distance / EARTH_RADIUS) / Math.sin(distance / EARTH_RADIUS);

        double x = A * Math.cos(lat1) * Math.cos(lon1) + B * Math.cos(lat2) * Math.cos(lon2);
        double y = A * Math.cos(lat1) * Math.sin(lon1) + B * Math.cos(lat2) * Math.sin(lon2);
        double z = A * Math.sin(lat1) + B * Math.sin(lat2);

        // 计算经度和纬度
        double lat = Math.atan2(z, Math.sqrt(x * x + y * y));
        double lon = Math.atan2(y, x);

        return new double[]{toDegrees(lat), toDegrees(lon)};
    }

    // 计算给定时间点的经纬度
    public static double[] calculatePointAtTime(double lat1, double lon1, double lat2, double lon2, double v1, double v2, double t1, double t2, double t) {
        // 计算从 A 到 B 的总时间
        double totalTime = t2 - t1;
        // 计算从 A 到任意点的时间差
        double dt = t - t1;
        // 计算总距离（单位为米）
        double D = calculateGreatCircleDistance(lat1, lon1, lat2, lon2);
        // 计算任意点距离（单位为米）
        double d = (dt / totalTime) * D; // 线性插值的角度

        // 计算位置插值
        double fraction = d / D;

        return calculateIntermediatePoint(lat1, lon1, lat2, lon2, fraction);
    }

    public static void main(String[] args) throws Exception {
        // 示例数据
//        double lat1 = 34.0;  // A 点纬度
//        double lon1 = 120.0; // A 点经度
//        double lat2 = 35.0;  // B 点纬度
//        double lon2 = 121.0; // B 点经度
//        double v1 = 222.22;  // A 点速度，单位 m/s（约800 km/h）
//        double t1 = 0;       // A 点时间
//        double t2 = 2;       // B 点时间
//        double t = 1;        // 任意点时间
//
//        double[] point = calculatePointAtTime(lat1, lon1, lat2, lon2, v1, v1, t1, t2, t);
//        System.out.println("Intermediate Point Latitude: " + point[0]);
//        System.out.println("Intermediate Point Longitude: " + point[1]);

//        GeometryFactory factory = new GeometryFactory();
//        Point point1 = factory.createPoint(new Coordinate(118.8911, 23.5312));
//        Point point2 = factory.createPoint(new Coordinate(117.209, 22.8207));
//
//        double distance1 = point1.distance(point2);
//
//        System.out.println(distance1);

    }

    public static Polygon createCircle0(double latitude, double longitude, double radius, GeometryFactory shapeFactory) throws Exception {
        return calculateCircleArea(latitude, longitude, radius, shapeFactory);
    }

    public static Polygon calculateCircleArea(double latitude, double longitude, double radius, GeometryFactory geometryFactory) throws Exception {
        // 创建JTS GeometryFactory
//        GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();
//        GeometryFactory geometryFactory = new GeometryFactory();

        // 生成圆形边界点
        Coordinate[] coordinates = generateCircleCoordinates(latitude, longitude, radius);

        // 创建多边形表示圆形区域
        Polygon circlePolygon = geometryFactory.createPolygon(coordinates);

        return circlePolygon;
        // 计算球面面积
//        return calculateSphericalArea(circlePolygon, latitude, longitude, radius);
    }

    private static Coordinate[] generateCircleCoordinates(double latitude, double longitude, double radius) {
        int numPoints = 64; // 圆周上的点数
        Coordinate[] coordinates = new Coordinate[numPoints + 1];

        for (int i = 0; i < numPoints; i++) {
            double angle = Math.toRadians((360.0 / numPoints) * i);
            double latOffset = (radius / EARTH_RADIUS) * Math.cos(angle);
            double lonOffset = (radius / (EARTH_RADIUS * Math.cos(Math.toRadians(latitude)))) * Math.sin(angle);

            double lat = latitude + Math.toDegrees(latOffset);
            double lon = longitude + Math.toDegrees(lonOffset);

            coordinates[i] = new Coordinate(lon, lat);
        }
        coordinates[numPoints] = coordinates[0]; // 闭合多边形

        return coordinates;
    }

    private static double calculateSphericalArea(Polygon polygon, double latitude, double longitude, double radius) throws Exception {
        // 使用默认的 WGS84 坐标参考系
        CoordinateReferenceSystem crs = DefaultGeographicCRS.WGS84;
        GeodeticCalculator calculator = new GeodeticCalculator(crs);

        double area = 0.0;
        Coordinate[] coordinates = polygon.getCoordinates();

        for (int i = 0; i < coordinates.length - 1; i++) {
            calculator.setStartingGeographicPoint(coordinates[i].x, coordinates[i].y);
            calculator.setDestinationGeographicPoint(coordinates[i + 1].x, coordinates[i + 1].y);
            double segmentLength = calculator.getOrthodromicDistance();

            // 计算每段对面积的贡献
            double latMid = Math.toRadians((coordinates[i].y + coordinates[i + 1].y) / 2);
            area += segmentLength * radius * Math.cos(latMid);
        }

        return Math.abs(area);
    }

}
