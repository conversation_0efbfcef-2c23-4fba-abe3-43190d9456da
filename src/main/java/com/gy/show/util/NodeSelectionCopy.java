//package com.gy.show.util;
//
//import lombok.Data;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
//public class NodeSelectionCopy {
//
//    /**
//     * 终端
//     */
//    @Data
//    public static class Terminal {
//        /**
//         * 终端序号
//         */
//        int id;
//
//        /**
//         * 纬度、经度、高度
//         */
//        double[] pos; // {latitude, longitude, altitude}
//
//        /**
//         * 对应 抗干扰状态帧 --> 有用信号强度 dB
//         */
//        double rss;
//
//        /**
//         *  对应 建链申请数据帧 --> 建链申请
//         */
//        int req;
//
//        /**
//         *  对应 抗干扰状态帧 --> 抗干扰手段决策结果
//         */
//        int antj;
//
//        public Terminal(int id, double[] pos, double rss, int req, int antj) {
//            this.id = id;
//            this.pos = pos;
//            this.rss = rss;
//            this.req = req;
//            this.antj = antj;
//        }
//    }
//
//    /**
//     * 节点（站）
//     */
//    @Data
//    public static class Node {
//        /**
//         * 纬度、经度、高度
//         */
//        double[] pos; // {latitude, longitude, altitude}
//
//        /**
//         * 节点参数，节点ID，测控距离，是否在使用 0 已使用 1 未使用
//         */
//        int[] para;  // {id, maxDistance, AvailEn}
//
//        public Node(double[] pos, int[] para) {
//            this.pos = pos;
//            this.para = para;
//        }
//    }
//
//    @Data
//    public static class Output {
//        /**
//         * 对应抗干扰控制帧 结果使能
//         */
//        int SlctEn;
//        int TarID;
//
//        /**
//         * 切换链路代号
//         */
//        int NodeID;
//
//        /**
//         * 链路切换方式
//         */
//        int AccHandEn;
//
//        /**
//         * 输出至前端系统弹性抗干扰决策结果-->开启状态
//         */
//        int SysAntj;
//
//        public Output(int SlctEn, int TarID, int NodeID, int AccHandEn, int SysAntj) {
//            this.SlctEn = SlctEn;
//            this.TarID = TarID;
//            this.NodeID = NodeID;
//            this.AccHandEn = AccHandEn;
//            this.SysAntj = SysAntj;
//        }
//    }
//
//    public static double[] func_lla2ecef(double[] lla) {
//        double a = 6378137.0; // Semi-major axis
//        double f = 1.0 / 298.257223563; // Flattening
//
//        double wd = Math.toRadians(lla[0]);
//        double jd = Math.toRadians(lla[1]);
//        double h = lla[2];
//
//        double e2 = f * (2 - f);
//        double N = a / Math.sqrt(1 - e2 * Math.sin(wd) * Math.sin(wd));
//
//        double x = (N + h) * Math.cos(wd) * Math.cos(jd);
//        double y = (N + h) * Math.cos(wd) * Math.sin(jd);
//        double z = (N * (1 - e2) + h) * Math.sin(wd);
//
//        return new double[]{x, y, z};
//    }
//
//    /**
//     * 随遇接入、切换
//     * @param InTer 实时上报的终端信息
//     * @param InNode 实时上报的节点信息
//     * @param InSchT 尚未使用的终端序号数组
//     * @param InCurN 当前正在使用的站ID，初始值为0
//     * @param ThrRSS 信号强度门限值
//     * @return
//     */
//    public static Output func_syjr(Terminal InTer, Node[] InNode, int[] InSchT, int InCurN, double ThrRSS) {
//        boolean EnAcc = Arrays.stream(InSchT).noneMatch(id -> id == InTer.id) || InTer.req == 1;
//        boolean EnHand = InTer.rss <= ThrRSS || InTer.req == 2 || InTer.antj == 1;
//        boolean EnAntj = InTer.antj == 5;
//
//        int AccessNodeID = 0;
//        int SlctEn = 0;
//        int node_avail = 0;
//        Integer TerID_Sp[] = {1, 2, 3, 4};//与HTCK站通道绑定的终端
//        Integer NodeId_Sp[] = {123,134};//对应两个航天站的id ----- 需根据最终确定的id修改 --- 若id存在随机变化的话，可以通过输入得到
//
//        if (EnAcc || EnHand || EnAntj) {
//            double[] TargetLoc = func_lla2ecef(InTer.pos);
//            double[][] NodeLoc = new double[InNode.length][3];
//            double[] TargetNodeDis = new double[InNode.length];
//
//            for (int i = 0; i < InNode.length; i++) {
//                NodeLoc[i] = func_lla2ecef(InNode[i].pos);
//                TargetNodeDis[i] = Math.sqrt(
//                        Math.pow(NodeLoc[i][0] - TargetLoc[0], 2) +
//                                Math.pow(NodeLoc[i][1] - TargetLoc[1], 2) +
//                                Math.pow(NodeLoc[i][2] - TargetLoc[2], 2)
//                );
//            }
//
//            List<Integer> AvailNodeIdx = new ArrayList<>();
//            for (int i = 0; i < InNode.length; i++) {
//                boolean ter_sp = Arrays.asList(TerID_Sp).contains(InTer.id);
//                boolean node_sp = Arrays.asList(NodeId_Sp).contains(InNode[i].para[0]);
//                if (ter_sp && node_sp) {
//                    node_avail = 1;
//                }
//                else {
//                    node_avail = InNode[i].para[2];
//                }
//
//                if (node_avail == 1 && TargetNodeDis[i] <= InNode[i].para[1]) {
//                    AvailNodeIdx.add(i);
//                }
//            }
//
//            if (EnHand || EnAntj) {
//                AvailNodeIdx.removeIf(idx -> InNode[idx].para[0] == InCurN);
//            }
//
//            if (!AvailNodeIdx.isEmpty()) {
//                AvailNodeIdx.sort((idx1, idx2) -> Double.compare(TargetNodeDis[idx2], TargetNodeDis[idx1])); // 改为降序
//                AccessNodeID = InNode[AvailNodeIdx.get(0)].para[0];
//                SlctEn = 1;
//            }
//
//        }
//
//        int AccHandEn = 0;
//        int SysAntj = 0;
//        if (EnAcc) {
//            AccHandEn = 3;
//        }
//        else if (EnHand) {
//            AccHandEn = 2;
//        }
//        else if (EnAntj) {
//            AccHandEn = 1;
//            SysAntj = 1;
//        }
//
//        return new Output(SlctEn, InTer.id, AccessNodeID,AccHandEn,SysAntj);
//    }
//
//    public static void main(String[] args) {
//        Terminal InTer = new Terminal(3, new double[]{30.567998, 105.12856, 0}, -16, 0,5);
//
//        Node[] InNode = {
//                new Node(new double[]{30.89345, 105.56452, 100}, new int[]{123, 100, 1}),
//                new Node(new double[]{30.83345, 105.53452, 0}, new int[]{134, 500000, 1}),
//                new Node(new double[]{30.90345, 105.36452, 100}, new int[]{156, 1000000, 1}),
//                new Node(new double[]{30.85345, 105.58452, 0}, new int[]{434, 1000000, 0})
//        };
//
//        int[] InSchT = {3, 4, 5};
//        int InCurN = 0;
//        double ThrRSS = -20;
//
//        Output result = func_syjr(InTer, InNode, InSchT, InCurN, ThrRSS);
//
//        System.out.println("Out.SlctEn = " + result.SlctEn);
//        System.out.println("Out.TarID = " + result.TarID);
//        System.out.println("Out.NodeID = " + result.NodeID);
//        System.out.println("Out.AccHandEn = " + result.AccHandEn);
//        System.out.println("Out.SysAntj = " + result.SysAntj);
//    }
//}
