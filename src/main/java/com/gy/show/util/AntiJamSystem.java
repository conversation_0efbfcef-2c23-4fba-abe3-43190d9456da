package com.gy.show.util;

import java.util.*;

public class AntiJamSystem {

    public static class State {
        /**
         * 测控链路站状态 格式：ID_可用通道号
         */
        int[] CKLinkState;

        /**
         * 当前正在使用的站ID，对应接入算法的参数
         */
        int[] termAccessStationId;

        /**
         * 终端的经纬高
         */
        List<double[]> termPos;

        public State(int[] CKLinkState, int[] termAccessStationId, List<double[]> termPos) {
            this.CKLinkState = CKLinkState;
            this.termAccessStationId = termAccessStationId;
            this.termPos = termPos;
        }
    }

    public static class AntiJam {
        /**
         * 终端序号
         */
        int termId;

        /**
         * 抗干扰状态信号帧 -> 干扰信号有无
         */
        int intfOrNot;

        /**
         * 抗干扰状态信号帧 -> 干扰强度
         */
        int intfStr;

        /**
         * 抗干扰状态信号帧 -> 干扰类型
         */
        int intfType;

        /**
         * 抗干扰状态信号帧 -> 有用信号有无
         */
        int signalOrNot;

        /**
         * 抗干扰状态信号帧 -> 有用信号强度
         */
        int signalStr;

        /**
         * 抗干扰状态信号帧 -> 抗干扰手段决策结果
         */
        int antiMode;

        /**
         * 抗干扰状态信号帧 -> 系统级抗干扰开启状态 (自主决策)
         */
        int antiState;

        /**
         * 抗干扰状态信号帧 -> 干扰频点
         */
        int intfFreq;

        /**
         * 抗干扰状态信号帧 -> 干扰带宽
         */
        int intfBand;

        /**
         * 抗干扰状态信号帧 -> 信号频点
         */
        int signalFreq;

        /**
         * 抗干扰状态信号帧 -> 信号带宽
         */
        int signalBand;

        /**
         * 抗干扰状态信号帧 -> 重构链路代号
         */
        int relinkId;

        public AntiJam(int termId, int intfOrNot, int intfStr, int intfType, int signalOrNot, int signalStr,
                       int antiMode, int antiState, int intfFreq, int intfBand, int signalFreq, int signalBand, int relinkId) {
            this.termId = termId;
            this.intfOrNot = intfOrNot;
            this.intfStr = intfStr;
            this.intfType = intfType;
            this.signalOrNot = signalOrNot;
            this.signalStr = signalStr;
            this.antiMode = antiMode;
            this.antiState = antiState;
            this.intfFreq = intfFreq;
            this.intfBand = intfBand;
            this.signalFreq = signalFreq;
            this.signalBand = signalBand;
            this.relinkId = relinkId;
        }
    }

    public static class SysParm {
        /**
         * 可用通道数量
         */
        int N;
        int[] TTCMode_ALL;
        int[] cksignFrq_ALL;
        int[] cksignBw_ALL;
        List<double[]> StatPos_ALL;

        public SysParm(int N, int[] TTCMode_ALL, int[] cksignFrq_ALL, int[] cksignBw_ALL, List<double[]> StatPos_ALL) {
            this.N = N;
            this.TTCMode_ALL = TTCMode_ALL;
            this.cksignFrq_ALL = cksignFrq_ALL;
            this.cksignBw_ALL = cksignBw_ALL;
            this.StatPos_ALL = StatPos_ALL;
        }
    }

    public static class DeciRes {
        int termId;
        int sysJudgeEnable;
        int linkSwitchMethod;
        int linkSwitchNum;
        int TTCMode;
        int cksignFrq;
        int cksignBw;

        @Override
        public String toString() {
            return "DeciRes {" +
                    "termId=" + termId +
                    ", sysJudgeEnable=" + sysJudgeEnable +
                    ", linkSwitchMethod=" + linkSwitchMethod +
                    ", linkSwitchNum=" + linkSwitchNum +
                    ", TTCMode=" + TTCMode +
                    ", cksignFrq=" + cksignFrq +
                    ", cksignBw=" + cksignBw +
                    '}';
        }
    }

    public static DeciRes sysAntiJam(State state, AntiJam antiJam, SysParm sysParm) {
        DeciRes deciRes = new DeciRes();
        deciRes.termId = 0;
        deciRes.sysJudgeEnable = 0;
        deciRes.linkSwitchMethod = 0;
        deciRes.linkSwitchNum = 0;
        deciRes.TTCMode = 0;
        deciRes.cksignFrq = 0;
        deciRes.cksignBw = 0;

        double[] disTermStat = new double[sysParm.N];
        for (int i = 0; i < sysParm.N; i++) {
            double[] pos1 = state.termPos.get(antiJam.termId - 1);
            double[] pos2 = sysParm.StatPos_ALL.get(i);
            disTermStat[i] = Math.sqrt(Math.pow(pos1[0] - pos2[0], 2) +
                    Math.pow(pos1[1] - pos2[1], 2) +
                    Math.pow(pos1[2] - pos2[2], 2));
        }

        Integer[] statPosSort = new Integer[sysParm.N];
        for (int i = 0; i < sysParm.N; i++) {
            statPosSort[i] = i;
        }
        Arrays.sort(statPosSort, Comparator.comparingDouble(i -> disTermStat[i]));

        if (antiJam.antiMode == 5) {
            for (int statIdx : statPosSort) {
                if (state.CKLinkState[statIdx] == 0 && statIdx != state.termAccessStationId[antiJam.termId - 1]) {
                    deciRes.linkSwitchNum = statIdx + 1;
                    deciRes.termId = antiJam.termId;
                    deciRes.sysJudgeEnable = 1;
                    deciRes.linkSwitchMethod = 1;
                    deciRes.TTCMode = sysParm.TTCMode_ALL[statIdx];
                    deciRes.cksignFrq = sysParm.cksignFrq_ALL[statIdx];
                    deciRes.cksignBw = sysParm.cksignBw_ALL[statIdx];
                    break;
                }
            }
        }

        return deciRes;
    }

    public static void main(String[] args) {
        int[] CKLinkState = {1, 1, 0, 1, 0, 0, 0, 0, 0, 0};
        int[] termAccessStationId = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
        List<double[]> termPos = Arrays.asList(
                new double[]{103, 30, 500},
                new double[]{103, 30, 500},
                new double[]{103, 30, 500},
                new double[]{103, 30, 500},
                new double[]{103, 30, 500},
                new double[]{103, 30, 500},
                new double[]{103, 30, 500},
                new double[]{103, 30, 500},
                new double[]{103, 30, 500},
                new double[]{103, 30, 500}
        );

        State state = new State(CKLinkState, termAccessStationId, termPos);

        AntiJam antiJam = new AntiJam(1, 1, 25, 3, 0, 0, 5, 0, 1750000, 3000, 0, 0, 0);

        List<double[]> statPosALL = Arrays.asList(
                new double[]{102.5, 34.5, 300},
                new double[]{102.5, 34.5, 300},
                new double[]{102.5, 34.5, 300},
                new double[]{102.5, 34.5, 300},
                new double[]{103.5, 35.5, 301},
                new double[]{103.5, 35.5, 301},
                new double[]{103.5, 35.5, 301},
                new double[]{103.5, 35.5, 301},
                new double[]{103.2, 30.2, 500},
                new double[]{105.5, 37.5, 303}
        );
        SysParm sysParm = new SysParm(10, new int[]{1, 1, 1, 1, 2, 2, 2, 2, 3, 3},
                new int[]{2070, 2070, 2070, 2070, 30000, 30000, 30000, 30000, 1810, 1810},
                new int[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}, statPosALL);

        DeciRes result = sysAntiJam(state, antiJam, sysParm);
        System.out.println(result);
    }
}

