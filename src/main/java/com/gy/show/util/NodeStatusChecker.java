package com.gy.show.util;

import java.util.HashMap;
import java.util.Map;

public class NodeStatusChecker {

    public static void main(String[] args) {
        // 构造节点连接的 map 集合
        Map<Integer, int[]> nodeMap = new HashMap<>();
        nodeMap.put(0, new int[]{0, 1, 1, 2, 2, 2});
        nodeMap.put(1, new int[]{});
//        nodeMap.put(2, new int[]{2, 2, 0, 2, 2, 2});
//        nodeMap.put(3, new int[]{2, 2, 2, 0, 2, 2});
//        nodeMap.put(4, new int[]{2, 2, 2, 2, 0, 1});
        nodeMap.put(2, new int[]{});
        nodeMap.put(3, new int[]{});
        nodeMap.put(4, new int[]{});
        nodeMap.put(5, new int[]{}); // 空数组

        // 调用函数检查节点状态
        Map<Integer, Integer> map = checkNodeStatus(nodeMap);
        System.out.println(map);
    }

    /**
     * 1 表示正常，无需处理
     * 2 表示需要做切换，在接入算法Terminal对象中增加节点状态参数
     * 3 表示终端退网，需要将数据库中对应终端状态改为未使用
     * @param nodeMap
     * @return
     */
    public static Map<Integer, Integer> checkNodeStatus(Map<Integer, int[]> nodeMap) {
        Map<Integer, Integer> result = new HashMap<>();
        int numNodes = nodeMap.size();

        // 遍历每个节点，检查其状态
        for (int i = 0; i < numNodes; i++) {
            int[] nodeArray = nodeMap.get(i);
            int status;

            if (nodeArray.length == 0) {
                // 如果节点数组为空，检查其他节点数组中是否有连接到该节点
                status = 3; // 默认其余状态
                for (int j = 0; j < numNodes; j++) {
                    if (j != i) {
                        int[] otherArray = nodeMap.get(j);
                        if (otherArray.length > i && otherArray[i] == 1) {
                            status = 2; // 需切换
                            break;
                        }
                    }
                }
            } else {
                // 如果节点数组中存在1，表示节点正常
                boolean hasConnection = false;
                for (int value : nodeArray) {
                    if (value == 1) {
                        hasConnection = true;
                        break;
                    }
                }
                status = hasConnection ? 1 : 3;
            }

            result.put(i, status);
        }
        return result;
    }
}

