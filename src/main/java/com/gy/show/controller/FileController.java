package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = "文件操作相关接口")
@RestController
@RequestMapping("/file")
public class FileController {

    @Autowired
    private FileService fileService;

    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public Result upload(@RequestPart(required = false) MultipartFile file,
                         @RequestParam("bzPath") String bzPath) {
        String fileId = fileService.upload(file, bzPath);
        return Result.ok(fileId);
    }

    @ApiOperation("查询文件")
    @GetMapping("/find/{fileId}")
    public Result upload(@PathVariable("fileId") String fileId) {
        String filePath = fileService.getFilePath(fileId);
        return Result.ok(filePath);
    }

    @PostMapping("/importTrackFile")
    @ApiOperation("导入轨迹文件")
    public Result importTrackFile(MultipartFile file) {
        String sessionId = fileService.importTrackFile(file);
        return Result.ok(sessionId);
    }

    @GetMapping("/loadTrackFile")
    @ApiOperation("加载轨迹文件")
    public Result loadTrackFile(@RequestParam("fileId") String fileId) {
        Object result = fileService.loadTrackFile(fileId);
        return Result.ok(result);
    }

    @GetMapping("/getImageByType")
    @ApiOperation("获取图片")
    public Result getImageByType(@RequestParam("type") Integer type) {
        String result = fileService.getImageByType(type);
        return Result.ok(result);
    }
}
