package com.gy.show.controller.external;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.external.SpaceTargetControlDTO;
import com.gy.show.entity.dto.external.StationDataDTO;
import com.gy.show.service.StationDataService;
import com.gy.show.service.StationSpaceStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

@Api(tags = "航天测控站状态相关接口")
@RestController
@RequestMapping("/external/space")
public class StationSpaceStatusController {

    @Autowired
    private StationSpaceStatusService stationSpaceStatusService;

    @Autowired
    private StationDataService stationDataService;

    @ApiOperation("过程控制命令")
    @PostMapping("/command/control")
    public Result spaceCommand(SpaceTargetControlDTO spaceTargetControlDTO) {
        // todo 暂时写死，没有postman
        SpaceTargetControlDTO.TargetControlCommand command1 = new SpaceTargetControlDTO.TargetControlCommand();
        command1.setSeq(1);
        command1.setIsOpen(2);

        SpaceTargetControlDTO.TargetControlCommand command2= new SpaceTargetControlDTO.TargetControlCommand();
        command2.setSeq(2);
        command2.setIsOpen(1);

        SpaceTargetControlDTO.TargetControlCommand command3 = new SpaceTargetControlDTO.TargetControlCommand();
        command3.setSeq(3);
        command3.setIsOpen(2);

        SpaceTargetControlDTO.TargetControlCommand command4 = new SpaceTargetControlDTO.TargetControlCommand();
        command4.setSeq(4);
        command4.setIsOpen(2);

        spaceTargetControlDTO.setCommands(Arrays.asList(command1, command2, command3, command4));


        stationSpaceStatusService.spaceCommand(spaceTargetControlDTO);
        return Result.ok();
    }

    @ApiOperation("状态查询命令")
    @PostMapping("/command/status")
    public Result statusCommand() {
        stationSpaceStatusService.statusCommand();
        return Result.ok();
    }

    @PostMapping("/remote/control")
    @ApiOperation("测试发送航天遥控指令")
    public Result remoteControlCommand(StationDataDTO stationDataDTO) throws InterruptedException {
        for (int i = 0; i < 100; i++) {
            stationDataService.remoteControlCommand(stationDataDTO);
            Thread.sleep(200);
        }

        return Result.ok();
    }

}
