package com.gy.show.controller.external;

import com.gy.show.common.Result;
import com.gy.show.constants.CacheConstant;
import com.gy.show.entity.dto.external.NodeSelectDTO;
import com.gy.show.entity.dto.external.TerminalAntiJammingDTO;
import com.gy.show.entity.dto.external.TerminalDataTransferDTO;
import com.gy.show.entity.dto.external.TerminalMachineDTO;
import com.gy.show.service.TerminalDataService;
import com.gy.show.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

@Api(tags = "终端相关接口")
@RestController
@RequestMapping("/external/terminal")
public class TerminalDataController {

    @Autowired
    private TerminalDataService terminalDataService;

    @ApiOperation("整机参数设置")
    @PostMapping("/setMachineParam")
    public Result setMachineParam(@RequestBody TerminalMachineDTO terminalMachineDTO) {
        terminalDataService.sendMachineParams(terminalMachineDTO);

        // 将节点ID加入缓存中，后续接入算法会使用该值
//        RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_CURRENT_STATION, terminalMachineDTO.getNodeId(), 8, TimeUnit.HOURS);

        return Result.ok();
    }

    @ApiOperation("数据传输控制")
    @PostMapping("/transfer")
    public Result dataTransfer(@RequestBody TerminalDataTransferDTO dataTransferDTO) {
        terminalDataService.dataTransfer(dataTransferDTO);
        return Result.ok();
    }

    @ApiOperation("根据终端编号获取终端所使用的体制")
    @GetMapping("/mode")
    public Result dataTransfer(@RequestParam("terminalSeq") Integer terminalSeq) {
        String mode = RedisUtil.StringOps.get(CacheConstant.TERMINAL_CURRENT_MODE + terminalSeq);
        return Result.ok(mode);
    }

    @ApiOperation("抗干扰控制帧")
    @PostMapping("/antiJamming")
    public Result antiJamming(@RequestBody TerminalAntiJammingDTO terminalAntiJammingDTO) {
        terminalDataService.sendAntiJammingParams(terminalAntiJammingDTO);
        return Result.ok();
    }

    @ApiOperation("手动确认切换节点")
    @PostMapping("/confirm")
    public Result confirmSwitch(@RequestBody @Valid NodeSelectDTO nodeSelectDTO) {
        terminalDataService.confirmSwitch(nodeSelectDTO);
        return Result.ok();
    }

    @ApiOperation("手动取消切换节点")
    @PostMapping("/cancel")
    public Result cancelSwitch(@RequestBody @Valid NodeSelectDTO nodeSelectDTO) {
        terminalDataService.cancelSwitch(nodeSelectDTO);
        return Result.ok();
    }

    @ApiOperation("获取当前节点接入开关")
    @GetMapping("/node/select")
    public Result nodeSelect() {
        String result = RedisUtil.StringOps.get(CacheConstant.NODE_SELECT);
        return Result.ok(result);
    }

    @ApiOperation("节点接入算法开关")
    @PostMapping("/node/select")
    public Result updateNodeSelect() {
        Result result = nodeSelect();
        Integer curr = Integer.parseInt(result.getData().toString());

        int res = curr == 1 ? 0 : 1;
        RedisUtil.StringOps.set(CacheConstant.NODE_SELECT, res + "");
        return Result.ok();
    }

    @ApiOperation("获取终端当前接入状态")
    @GetMapping("/getTerminalStatus")
    public Result getTerminalStatus(@RequestParam("terId") Integer terId) {
        String status = RedisUtil.StringOps.get(CacheConstant.TERMINAL_ALL_STATUS + terId);
        return Result.ok(StringUtils.isNotBlank(status) ? 1 : 0);
    }

}
