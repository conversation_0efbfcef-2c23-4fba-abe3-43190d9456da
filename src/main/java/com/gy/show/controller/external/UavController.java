package com.gy.show.controller.external;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.ConfirmScheduleDTO;
import com.gy.show.service.ExternalDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "无人机操控端相关接口")
@RestController
@RequestMapping("/external/plane")
public class UavController {

    @Autowired
    private ExternalDataService externalDataService;

    @ApiOperation("发送数据")
    @PostMapping("/send")
    public Result sendMessage(@RequestBody List<ConfirmScheduleDTO> confirmScheduleDTOs) {
        externalDataService.packageScheduleResult(confirmScheduleDTOs);
        return Result.ok();
    }

}
