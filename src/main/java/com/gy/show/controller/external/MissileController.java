package com.gy.show.controller.external;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.external.MissileDTO;
import com.gy.show.service.MissileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "导弹测控站相关接口")
@RestController
@RequestMapping("/external/missile")
public class MissileController {

    @Autowired
    private MissileService missileService;

    @ApiOperation("发送数据")
    @PostMapping("/send")
    public Result sendMessage(@RequestBody MissileDTO missileDTO) {
        missileService.sendMessage(missileDTO);
        return Result.ok();
    }

}
