package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.TaskTargetRelationDTO;
import com.gy.show.service.TaskTargetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 任务目标相关接口
 */
@RestController
@RequestMapping("/task/target")
@Api(tags = "任务目标相关接口")
public class TaskTargetController extends BaseController {

    @Autowired
    private TaskTargetService taskTargetService;

    @ApiOperation("根据任务ID查询所有目标及航迹")
    @GetMapping("/listByTask")
    public Result listByTask(@RequestParam("taskId") String taskId,
                             @RequestParam(value = "isInterpolated", required = false, defaultValue = "true") Boolean isInterpolated) {
        TaskTargetRelationDTO result = taskTargetService.listByTask(taskId, isInterpolated);
        return Result.ok(result);
    }

    @ApiOperation("新增目标接口，包含目标航迹以及目标位置等信息")
    @PostMapping("/storeTarget")
    public Result storeTarget(@Valid @RequestBody TaskTargetRelationDTO taskTargetRelationDTO) {
        taskTargetService.storeTarget(taskTargetRelationDTO);
        return Result.ok();
    }

    @ApiOperation("修改目标接口，包含及目标位置、航迹宽度颜色等信息")
    @PutMapping("/updateTarget")
    public Result updateTarget(@Valid @RequestBody TaskTargetRelationDTO taskTargetRelationDTO) {
        taskTargetService.updateTarget(taskTargetRelationDTO);
        return Result.ok();
    }

    @ApiOperation("删除目标航迹接口")
    @DeleteMapping("/removeTrack")
    public Result removeTrack(@RequestParam("relationId") String relationId) {
        taskTargetService.removeTrack(relationId);
        return Result.ok();
    }

    @ApiOperation("根据目标ID和任务ID删除目标,同步会删除关联航迹")
    @DeleteMapping("/removeTargetAndTrack")
    public Result removeTargetAndTrack(@RequestParam("taskId") String taskId,
                                       @RequestParam("targetId") String targetId) {
        taskTargetService.removeTargetAndTrack(taskId, targetId);
        return Result.ok();
    }
}
