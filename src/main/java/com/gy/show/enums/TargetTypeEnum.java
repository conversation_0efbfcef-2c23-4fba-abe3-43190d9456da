package com.gy.show.enums;

import java.util.Arrays;

public enum TargetTypeEnum {


    CAR(9, "00"),

    SHIP(10, "01"),

    AIRPLANE(8, "02");


    private Integer code;

    private String hexCode;

    TargetTypeEnum(Integer code, String hexCode) {
        this.code = code;
        this.hexCode = hexCode;
    }

    public static TargetTypeEnum getEnumByHexCode(String hexCode) {
        return Arrays.stream(TargetTypeEnum.values()).filter(f -> f.getHexCode().equals(hexCode)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getHexCode() {
        return hexCode;
    }

    public void setHexCode(String hexCode) {
        this.hexCode = hexCode;
    }
}
