package com.gy.show.enums;

import java.util.Arrays;
import java.util.Objects;

public enum WebSocketTypeEnum {

    PROCESS(1, "态势模拟推演进度推送"),

    SATELLITE(2, "卫星"),

    SCHEDULE(3, "一级资源调度"),

    FILE(4, "文件上传提醒"),

    FULL_VIEW_TARGET(5, "全景态势-目标数据"),

    FULL_VIEW_TERMINAL(6, "全景态势-终端数据"),

    FULL_VIEW_SPACE_STATION(7, "全景态势-航天站数据"),

    FULL_VIEW_MISSILE_STATION(8, "全景态势-导弹测控站数据"),

    FULL_VIEW_UAV_STATION(9, "全景态势-无人机测控站数据"),

    GLOBAL_NODE_SWITCH(10, "全局推送-站点切换通知"),

    FULL_VIEW_INTERACTION_LOG(11, "交互日志数据"),

    FULL_VIEW_STATION_LOG(12, "测控站日志数据"),

    FULL_VIEW_STATION_TEST(13, "态势生成时间数据");


    private Integer code;

    private String message;

    WebSocketTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static WebSocketTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(WebSocketTypeEnum.values()).filter(f -> Objects.equals(f.getCode(), code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
