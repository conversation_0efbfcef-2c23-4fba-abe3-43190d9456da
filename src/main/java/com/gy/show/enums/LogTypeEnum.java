package com.gy.show.enums;

import java.util.Arrays;

public enum LogTypeEnum {


    INFO(1, "INFO"),

    ERROR(2, "ERROR");


    private Integer code;

    private String message;

    LogTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static LogTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(LogTypeEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
