package com.gy.show.enums;

import java.util.Arrays;

public enum TerminalModeEnum {


    NO_SWITCH(0, "不切换"),

    KA_1(1, "Ka-1"),

    S_2(2, "S-2"),

    S_3(3, "S-3"),

    S_4(4, "S-4");


    private Integer code;

    private String message;

    TerminalModeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static TerminalModeEnum getEnumByCode(Integer code) {
        return Arrays.stream(TerminalModeEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
