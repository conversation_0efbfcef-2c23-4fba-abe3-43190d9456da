package com.gy.show.enums;

/**
 * 站点/节点切换状态确认枚举类
 */
public enum NodeSwitchStateEnum {

    PENDING("pending"),

    CONFIRMED("confirmed"),

    SUCCESS("success"),

    FAILED("failed"),

    CANCEL("cancel"),

    TIMEOUT("timeout");

    private String message;

    NodeSwitchStateEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}
