package com.gy.show.enums;

import java.util.Arrays;

/**
 * 外部数据包类型
 */
public enum ExternalDataTypeEnum {


    BUSINESS_DATA(1, "业务数据", "bz"),

    CAR_DATA(2, "导弹用户中心", "car"),

    SHIP_DATA(3, "无人艇用户中心", "ship"),

    PLANE_DATA(4, "无人机用户中心", "plane");


    private Integer code;

    private String message;

    private String type;

    ExternalDataTypeEnum(Integer code, String message, String type) {
        this.code = code;
        this.message = message;
        this.type = type;
    }

    public static ExternalDataTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(ExternalDataTypeEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getType() {
        return type;
    }
}
