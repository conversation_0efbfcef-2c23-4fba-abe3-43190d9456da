package com.gy.show.enums;

import java.util.Arrays;

public enum ScheduleTypeEnum {


    LJ(2, "随遇接入任务"),

    YGH(1, "预规划任务");


    private Integer code;

    private String message;

    ScheduleTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ScheduleTypeEnum getEnumByName(String name) {
        return Arrays.stream(ScheduleTypeEnum.values()).filter(f -> f.getMessage().equals(name)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
