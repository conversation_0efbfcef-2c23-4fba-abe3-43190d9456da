package com.gy.show.task;

import com.gy.show.constants.CacheConstant;
import com.gy.show.enums.NodeSwitchStateEnum;
import com.gy.show.service.TerminalDataService;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.gy.show.constants.CacheConstant.NODE_SWITCH;

@Slf4j
@EnableScheduling
@Component
public class TaskSchedule {

    @Autowired
    private TerminalDataService terminalDataService;

    @Scheduled(fixedRateString = "${schedule.fixed-rate}")
    public void fixedSchedule() {
//        log.info("开始执行周期调度任务");
//        scheduleService.startSchedule();
//        log.info("执行周期调度任务结束");
    }

    @Scheduled(fixedRate = 1000)
    public void checkSwitchTimeout() throws IOException {
        // 使用 RedisConnection 执行 SCAN
        Cursor<byte[]> cursor = RedisUtil.getRedisTemplate().execute((RedisCallback<Cursor<byte[]>>) connection -> {
            // 构建 SCAN 参数
            ScanOptions scanOptions = ScanOptions.scanOptions()
                    .match(NODE_SWITCH + "*")  // 匹配键模式
                    .count(100)             // 每次扫描数量
                    .build();

            // 使用原生连接执行 SCAN
            return connection.scan(scanOptions);
        });
        try {
            while (cursor.hasNext()) {
                // 将 byte[] 转换为 String
                String key = new String(cursor.next(), StandardCharsets.UTF_8);

                // 处理超时逻辑
                processKeyForTimeout(key);
            }
        } finally {
            if (cursor != null) {
                cursor.close(); // 必须关闭游标释放资源
            }
        }
    }

    private void processKeyForTimeout(String key) {
        Map<Object, Object> state =RedisUtil.HashOps.hEntries(key);

        if (NodeSwitchStateEnum.PENDING.getMessage().equals(state.get("status"))) {
            long createTime = Long.parseLong((String) state.get("createTime"));
            long timeDuration = System.currentTimeMillis() - createTime;
            if (timeDuration > 31000) {
                // 节点过期 自动切换
                processTimeout(key);
            }  else {
                // 通知前端有节点切换，是否需要做切换
                terminalDataService.notifyFrontNodeSwitch(30 - (timeDuration / 1000), state, key);
            }
        }
        else if (NodeSwitchStateEnum.CONFIRMED.getMessage().equals(state.get("status"))) {
            // 用户手动确认处理
            processConfirmed(key);
        }
    }

    private void processConfirmed(String key) {
        log.info("【节点切换】用户手动确认切换，开始执行节点切换流程");
        String luaScript =
                "if redis.call('HGET', KEYS[1], 'status') == 'confirmed' then " +
                        "redis.call('HSET', KEYS[1], 'status', 'success') " +
                        "return 1 " +
                        "else " +
                        "return 0 " +
                        "end";

        RedisUtil.getRedisTemplate().execute(
                new DefaultRedisScript<>(luaScript, Long.class),
                Collections.singletonList(key),
                new Object[]{}
        );
        // 执行最终切换
        executeFinalSwitch(key.split(":")[2]);
    }

    private void processTimeout(String key) {
        log.info("【节点切换】已到指定时间用户没有操作，开始执行自动节点切换流程");
        String terminalId = key.split(":")[2];
        String luaScript =
                "if redis.call('HGET', KEYS[1], 'status') == 'pending' then " +
                        "redis.call('HSET', KEYS[1], 'status', 'timeout') " +
                        "return 1 " +
                        "else " +
                        "return 0 " +
                        "end";

        Long result = RedisUtil.getRedisTemplate().execute(
                new DefaultRedisScript<>(luaScript, Long.class),
                Collections.singletonList(key),
                new Object[]{}
        );

        if (result != null && result == 1) {
            // 执行最终切换
            executeFinalSwitch(terminalId);
        }
    }

    private void executeFinalSwitch(String terminalId) {
        // 刷新锁的时间
        RedisUtil.KeyOps.expire(CacheConstant.NODE_SELECT_LOCK + terminalId, 30, TimeUnit.SECONDS);

        // 执行最终切换
        terminalDataService.executeFinalSwitch(terminalId);
    }

}
