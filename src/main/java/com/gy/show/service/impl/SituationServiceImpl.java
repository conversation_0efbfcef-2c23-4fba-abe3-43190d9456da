package com.gy.show.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.*;
import com.gy.show.entity.vo.DragParamVO;
import com.gy.show.entity.vo.DragReturnVO;
import com.gy.show.entity.vo.PointVO;
import com.gy.show.entity.vo.TrackVO;
import com.gy.show.enums.DataTypeEnum;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.*;
import com.gy.show.util.*;
import com.gy.show.ws.GlobalServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.geom.Point2D;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.gy.show.constants.CacheConstant.POINT_CACHE_PREFIX;

@Slf4j
@Service
public class SituationServiceImpl implements SituationService {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private RequirementInfoService requirementInfoService;

    @Autowired
    private TaskTargetService taskTargetService;

    @Autowired
    private RequirementTargetTrackService trackService;

    @Autowired
    private TaskEquipmentRelationService taskEquipmentRelationService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private SatelliteService satelliteService;

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @Autowired
    private GlobalServer globalServer;

    @Autowired
    private CommonMapper commonMapper;

    private static final Integer SHARD_VALUE = 1;

    @Override
    public Map<String, Object> getDataByTasks(List<String> taskIds) {
        Collection<RequirementTask> tasks = requirementTaskService.listByIds(taskIds);
        List<String> relationIds = tasks.stream()
                .map(RequirementTask::getTargetRelationId)
                .collect(Collectors.toList());

        Collection<TaskTargetRelation> taskTargetRelations = taskTargetService.listByIds(relationIds);
        Map<String, List<TaskTargetRelation>> groupByRelation0 = taskTargetRelations.stream()
                .collect(Collectors.groupingBy(TaskTargetRelation::getId));

        // 查询目标的航迹信息
        List<RequirementTargetTrack> tracks = trackService.queryTrackByIds(relationIds);

        Map<String, List<RequirementTargetTrack>> groupByRelation1 = tracks.stream()
                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId));

        // 构建任务和其目标、航迹的层级结构
        List<TaskWithTargets> taskWithTargetsList = tasks.stream()
                .map(task -> {
                    List<TargetWithTracks> targetsWithTracks = groupByRelation0.getOrDefault(task.getTargetRelationId(), Collections.emptyList()).stream()
                            .map(target -> new TargetWithTracks(target, groupByRelation1.getOrDefault(target.getId(), Collections.emptyList()), target.getType()))
                            .collect(Collectors.toList());

                    TaskWithTargets taskWithTargets = new TaskWithTargets(task, targetsWithTracks);
                    taskWithTargets.setTargetId(targetsWithTracks.get(0).getTarget().getTargetId());
                    return taskWithTargets;
                })
                .collect(Collectors.toList());

        log.info("目标数据查询结束,查询结果:{}", taskWithTargetsList);

        /**
         * 查询任务设备关联表
         */
        List<DataEquipmentOccupancy> taskEquipmentRelations = dataEquipmentOccupancyService.queryEquipmentByTaskIds(taskIds);

        Map<String, Object> result = new HashMap<>();
        result.put("targets", taskWithTargetsList);
        result.put("equipments", taskEquipmentRelations);
        return result;
    }

    @Override
    public Map<String, Object> getData(String id) {
        /**
         * 查询目标以及航迹
         * 1 先查询需求下有哪些任务
         * 2 根据任务查询相关联的目标列表
         * 3 查询目标的航迹信息
         * */
        List<RequirementTask> tasks = requirementTaskService.getTaskByRequirement(id);

        List<String> taskIds = tasks.stream()
                .map(RequirementTask::getId)
                .collect(Collectors.toList());

        Map<String, Object> data = new HashMap<>();
        List<DataEquipmentOccupancy> dataEquipmentOccupancies = dataEquipmentOccupancyService.list(Wrappers.<DataEquipmentOccupancy>lambdaQuery()
                .in(DataEquipmentOccupancy::getTaskId, taskIds));

        List<DataEquipmentOccupancyDTO> equipments = dataEquipmentOccupancies.stream()
                .map(DataEquipmentOccupancy::convert)
                .collect(Collectors.toList());

        // 主表ID集合
        List<String> generalIds = dataEquipmentOccupancies
                .stream().map(DataEquipmentOccupancy::getGeneralId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(generalIds)) {
            Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(generalIds);
            Map<String, List<DataGeneral>> groupById = dataGenerals.stream()
                    .collect(Collectors.groupingBy(DataGeneral::getId));

            for (DataEquipmentOccupancyDTO record : equipments) {
                List<DataGeneral> generalList = groupById.get(record.getGeneralId());

                if (CollUtil.isNotEmpty(generalList)) {
                    DataGeneral general = generalList.get(0);

                    Map<String, Object> equipmentDetail = commonMapper.getOne(general.getTableName(), record.getEquipmentId());
                    record.setEquipmentDetail(equipmentDetail);
                    record.setDataType(general.getDataType());
                    record.setType(general.getTableComment());
                }
            }
        }

        List<String> relationIds = tasks.stream()
                .map(RequirementTask::getTargetRelationId)
                .collect(Collectors.toList());

        Collection<TaskTargetRelation> taskTargetRelations = taskTargetService.listByIds(relationIds);

        List<TaskTargetRelationDTO> targets = taskTargetRelations.stream()
                .map(task -> {
                    TaskTargetRelationDTO targetRelationDTO = task.convert();
                    DataGeneral general = dataGeneralService.getById(task.getGeneralId());
                    targetRelationDTO.setDataType(general.getDataType());
                    targetRelationDTO.setTargetType(general.getTableComment());
                    return targetRelationDTO;
                }).collect(Collectors.toList());


        // 设备去重
        List<DataEquipmentOccupancyDTO> distinctEquipments = new ArrayList<>(equipments.stream()
                .collect(Collectors.toMap(
                        DataEquipmentOccupancyDTO::getEquipmentId,
                        Function.identity(),
                        (e, r) -> e
                )).values());

        data.put("targets", targets);
        data.put("equipments", distinctEquipments);

        return data;

//        List<TaskTargetRelation> relationList = taskTargetService.listByTaskIds(taskIds);
//        Map<String, List<TaskTargetRelation>> groupByRelation0 = relationList.stream()
//                .collect(Collectors.groupingBy(TaskTargetRelation::getTaskId));
//
//        // 查询目标的航迹信息
//        List<String> relationIds = relationList.stream()
//                .map(TaskTargetRelation::getId)
//                .collect(Collectors.toList());
//
//        List<RequirementTargetTrack> tracks = trackService.queryTrackByIds(relationIds);
//
//        Map<String, List<RequirementTargetTrack>> groupByRelation1 = tracks.stream()
//                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId));
//
//        // 构建任务和其目标、航迹的层级结构
//        List<TaskWithTargets> taskWithTargetsList = tasks.stream()
//                .map(task -> {
//                    List<TargetWithTracks> targetsWithTracks = groupByRelation0.getOrDefault(task.getId(), Collections.emptyList()).stream()
//                            .map(target -> new TargetWithTracks(target, groupByRelation1.getOrDefault(target.getId(), Collections.emptyList()), target.getType()))
//                            .collect(Collectors.toList());
//                    return new TaskWithTargets(task, targetsWithTracks);
//                })
//                .collect(Collectors.toList());
//
//        log.info("目标数据查询结束,查询结果:{}", taskWithTargetsList);
//
//        /**
//         * 查询任务设备关联表
//         */
//        List<DataEquipmentOccupancy> taskEquipmentRelations = dataEquipmentOccupancyService.queryEquipmentByTaskIds(taskIds);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("targets", taskWithTargetsList);
//        result.put("equipments", taskEquipmentRelations);
//
//        // 返回构建的层级结构
//        return result;
    }

    @Override
    public Map<String, Object> handleDragOrPlay(DragParamVO vo) {
        // 创建返回结果的map
        Map<String, Object> resultMap = new HashMap<>();
        // 创建用于存储拖拽或播放结果的列表
        List<DragReturnVO> resultList;

        // 将时间点从毫秒转换为秒，并设置默认步长为1
        vo.setTimePoint(vo.getTimePoint());
        vo.setStep(Objects.nonNull(vo.getStep()) ? vo.getStep() : 1);

        // 获取总时长
        Integer totalTime = getTotalTime(vo);

        // 根据是否是拖拽操作调用不同的处理方法
        resultList = vo.getDrag() ? processDrag(vo, totalTime) : processPlay(vo, totalTime);

        noticeClientMessage("开始计算资源数据...", 60);
        // 根据参数计算卫星的位置等信息
        Map<String, Map<String, Object>> satellite = satelliteCalculate(vo);
        noticeClientMessage("资源数据计算完成...", 100);

        // 后置过滤器 根据传入的目标参数、资源参数来过滤最终结果
        List<DragReturnVO> filterResult = postFilterData(resultList, vo);

        // 计算目标是否在设备的侦查 范围内
        List<Map<String, Object>> targetEquipmentRelation = calculateEquipmentScout(filterResult, vo);

        // 计算任务进度
        List<RequirementTaskDTO> taskDTOS = taskProcess(vo, targetEquipmentRelation);

        // 获取当前时间点的信号参数脚本数据
        resultMap.put("target", filterResult);
        resultMap.put("satellite", satellite);
        resultMap.put("task", taskDTOS);
        resultMap.put("targetEquipmentRelation", targetEquipmentRelation);
        resultMap.put("totalTime", totalTime);

        return resultMap;
    }

    /**
     * 计算目标是否在设备的侦查 范围内
     * @param resultList
     * @param vo
     * @return
     */
    private List<Map<String, Object>> calculateEquipmentScout(List<DragReturnVO> resultList, DragParamVO vo) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultList)) {
            List<DataEquipmentOccupancy> dataEquipmentOccupancies = dataEquipmentOccupancyService.queryEquipmentByTaskIds(vo.getTaskIds());

            List<String> generaIds = dataEquipmentOccupancies.stream()
                    .map(DataEquipmentOccupancy::getGeneralId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(generaIds)) {
                Map<String, List<DataGeneral>> groupByGneral = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery()
                                .in(DataGeneral::getId, generaIds)
                                .in(DataGeneral::getDataType, Arrays.asList(1, 2, 8))) // 只计算地基平台
                        .stream()
                        .collect(Collectors.groupingBy(DataGeneral::getId));

                List<Map<String, Object>> equipments = dataEquipmentOccupancies.stream()
                        .distinct()
                        .map(data -> {
                            DataGeneral dataGeneral = groupByGneral.get(data.getGeneralId()).get(0);
                            Map<String, Object> equipment = commonMapper.getOne(dataGeneral.getTableName(), data.getEquipmentId());

                            return equipment;
                        })
                        .collect(Collectors.toList());

                for (Map<String, Object> equipment : equipments) {
                    //
                    String longitude = equipment.get("longitude").toString();
                    String latitude = equipment.get("latitude").toString();
                    double radius = Double.parseDouble(equipment.get("radius") != null ? equipment.get("radius").toString() : equipment.get("bottomRadius").toString());

                    for (DragReturnVO dragReturnVO : resultList) {
                        if (CollUtil.isEmpty(dragReturnVO.getPoints())) continue;
                        // 获取当前目标的经纬度
                        PointVO pointVO = dragReturnVO.getPoints().get(0);

                        Point2D.Double equipmentPoint = GISUtils.parsePoint2DDouble(longitude, latitude);
                        Point2D.Double targetPoint = GISUtils.parsePoint2DDouble(pointVO.getX() + "", pointVO.getY() + "");

                        // 计算实时相对距离
                        double distance = GISUtils.getDistanceNew(equipmentPoint, targetPoint);
                        if (distance < radius) {
                            Map<String, Object> relation = new HashMap<>();
                            relation.put("equipmentId", equipment.get("id"));
                            relation.put("targetId", dragReturnVO.getMbId());

                            result.add(relation);
                        }
                    }
                }
            }

        }

        return result;
    }

    /**
     * 计算每个任务的进度
     *
     * @param vo
     * @param targetEquipmentRelation
     */
    private List<RequirementTaskDTO> taskProcess(DragParamVO vo, List<Map<String, Object>> targetEquipmentRelation) {
        // 获取当前时间点
        LocalDateTime currentTime = vo.getStartTime().plusSeconds(vo.getTimePoint());
        Collection<RequirementTask> tasks = requirementTaskService.listByIds(vo.getTaskIds());
        List<Map<String,Object>> relation = new ArrayList<>();
        List<RequirementTaskDTO> taskDTOS = tasks.stream()
                .map(task -> {
                    RequirementTaskDTO taskDTO = task.convert();
                    // 任务开始时间在当前时间之后，说明该任务还没开始
                    Double taskProcess = 0d;


                    IPage<DataEquipmentOccupancyDTO> equipmentByTask = dataEquipmentOccupancyService.getEquipmentByTask(-1, -1, task.getId());
                    long taskDuration = 0;
                    // 大于0说明有资源使用，任务才会继续往下推进
                    if (equipmentByTask.getRecords().size() > 0) {
                        for (DataEquipmentOccupancyDTO occupancyDTO : equipmentByTask.getRecords()) {
                            if (occupancyDTO.getStartTime().isBefore(currentTime) || occupancyDTO.getStartTime().isEqual(currentTime)) {
                                // 判断是否在侦察范围内
                                TaskTargetRelation targetRelation = taskTargetService.getById(task.getTargetRelationId());
                                List<Map<String, Object>> relationTargets = targetEquipmentRelation.stream()
                                        .filter(t -> t.get("targetId").equals(targetRelation.getTargetId()) && t.get("equipmentId").equals(occupancyDTO.getEquipmentId()))
                                        .collect(Collectors.toList());

                                // 判断线
                                if (relationTargets.size() > 0 && occupancyDTO.getEndTime().isAfter(currentTime)) {
                                    relation.addAll(relationTargets);
                                }

                                Duration duration;RequirementTaskDTO.Period period;
                                if (occupancyDTO.getEndTime().isAfter(currentTime)) {
                                    duration = Duration.between(occupancyDTO.getStartTime(), currentTime);

                                    period = new RequirementTaskDTO.Period();
                                    period.setStartTime(occupancyDTO.getStartTime());
                                    period.setEndTime(currentTime);
                                    period.setEquipmentName(occupancyDTO.getEquipmentDetail().get("name").toString());
                                    period.setEquipmentId(occupancyDTO.getEquipmentId());
                                    period.setTargetId(targetRelation.getTargetId());

                                    // 如果大于总时长，说明任务已经完成，进度则为100%
//                                    if (duration.getSeconds() > totalTime.getSeconds()) {
//                                        taskProcess = 1d;
//                                    } else {
//                                        taskProcess = (double) duration.getSeconds() / totalTime.getSeconds();
//                                    }

                                } else {
                                    // 不在范围内说明当前时间已经超出了离开时间
                                    duration = Duration.between(occupancyDTO.getStartTime(), occupancyDTO.getEndTime());

                                    period = new RequirementTaskDTO.Period();
                                    period.setStartTime(occupancyDTO.getStartTime());
                                    period.setEndTime(occupancyDTO.getEndTime());
                                    period.setEquipmentName(occupancyDTO.getEquipmentDetail().get("name").toString());
                                }

                                List<RequirementTaskDTO.Period> periods = taskDTO.getPeriods();
                                periods.add(period);
                                taskDuration += duration.getSeconds();
                            }
                        }
                        // 任务总时长
                        Duration totalTime = Duration.between(task.getStartTime(), task.getEndTime());

                        taskProcess = (double) taskDuration / totalTime.getSeconds();
                        if (taskProcess > 1.0) {
                            taskProcess = 1.0;
                        }
                    }

                    taskDTO.setProcess(Double.parseDouble(String.format("%.2f", taskProcess * 100)));
                    taskDTO.setCurrentTime(currentTime);
                    return taskDTO;
                })
                .collect(Collectors.toList());

        targetEquipmentRelation.clear();
        targetEquipmentRelation.addAll(relation);

        return taskDTOS;
    }

    /**
     * 后置过滤器 根据传入的目标参数、资源参数来过滤最终结果
     * @param resultMap
     * @param vo
     */
    private List<DragReturnVO> postFilterData(List<DragReturnVO> resultMap, DragParamVO vo) {
        List<DragReturnVO> filterResult = resultMap;
        // 获取当前时间点
        LocalDateTime currentTime = vo.getStartTime().plusSeconds(vo.getTimePoint());
        if (vo.getIsSimulate() != null && vo.getIsSimulate()) {

            // 根据目标过滤
            if (StringUtils.isNotBlank(vo.getTargetId())) {
                filterResult = resultMap.stream()
                        .filter(r -> r.getMbId().equals(vo.getTargetId()))
                        .collect(Collectors.toList());
            } else if (StringUtils.isNotBlank(vo.getEquipmentId())) {
                // 获取到关联目标
                List<TaskTargetRelation> targetRelations = getTargetByEquipment(vo.getEquipmentId(), vo.getRequirementId(), currentTime);

                List<String> targetId = targetRelations.stream()
                        .map(TaskTargetRelation::getTargetId)
                        .collect(Collectors.toList());

                filterResult = resultMap.stream()
                        .filter(r -> targetId.contains(r.getMbId()))
                        .collect(Collectors.toList());
            } else if (StringUtils.isNotBlank(vo.getFilterTaskId())) {
                // 查询目标id
                RequirementTask task = requirementTaskService.getById(vo.getFilterTaskId());

                TaskTargetRelation targetRelation = taskTargetService.getById(task.getTargetRelationId());

                filterResult = resultMap.stream()
                        .filter(r -> r.getMbId().equals(targetRelation.getTargetId()))
                        .collect(Collectors.toList());
            }
        }
        return filterResult;
    }

    private List<TaskTargetRelation> getTargetByEquipment(String equipmentId, String requirementId, LocalDateTime currentTime) {
        // 获取当前时间点
        List<RequirementTask> tasks = requirementTaskService.getTaskByRequirement(requirementId);

        List<String> taskIds = tasks.stream()
                .map(RequirementTask::getId)
                .collect(Collectors.toList());
        List<DataEquipmentOccupancy> equipmentOccupancies = dataEquipmentOccupancyService
                .list(Wrappers.<DataEquipmentOccupancy>lambdaQuery()
                        .in(DataEquipmentOccupancy::getTaskId, taskIds)
                .eq(DataEquipmentOccupancy::getEquipmentId, equipmentId)
                .le(DataEquipmentOccupancy::getStartTime, currentTime)
                .ge(DataEquipmentOccupancy::getEndTime, currentTime));

        if (CollUtil.isNotEmpty(equipmentOccupancies)) {
            List<String> ids = equipmentOccupancies.stream()
                    .map(DataEquipmentOccupancy::getTaskId)
                    .collect(Collectors.toList());

            List<String> targetRelationIds = requirementTaskService.listByIds(ids).stream()
                    .map(RequirementTask::getTargetRelationId)
                    .collect(Collectors.toList());

//        List<String> targetTaskIds = equipmentOccupancies.stream()
//                .map(DataEquipmentOccupancy::getTaskId)
//                .collect(Collectors.toList());

            // 获取到关联目标
            return new ArrayList<>(taskTargetService.listByIds(targetRelationIds));
        } else {
            return new ArrayList<>();
        }

    }

    private Map<String, Map<String, Object>> satelliteCalculate(DragParamVO vo) {
        Map<String, Object> data = getDataByTasks(vo.getTaskIds());

        Object equip = data.get("equipments");
        if (equip != null) {
            List<DataEquipmentOccupancy> deo = (List<DataEquipmentOccupancy>) equip;
            List<String> generalIds = deo.stream().map(DataEquipmentOccupancy::getGeneralId)
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(generalIds)) return new HashMap<>();
            Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(generalIds);

            List<DataGeneral> satellite = dataGenerals.stream()
                    .filter(g -> g.getDataType().equals(DataTypeEnum.SKY_PLATFORM.getCode()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(satellite)) return new HashMap<>();
        }
//        Collection<RequirementTask> tasks = requirementTaskService.listByIds(vo.getTaskIds());
//
//        Map<String, Object> data = new HashMap<>();
//        // 数据聚合
//        for (RequirementTask task : tasks) {
//            Map<String, Object> temp = getData(task.getRequirementId());
//
//            List<TaskWithTargets> targets = data.get("targets") != null ? (List<TaskWithTargets>) data.get("targets") : new ArrayList<>();
//
//            List<TaskWithTargets> tempTarget = temp.get("targets") != null ? (List<TaskWithTargets>) temp.get("targets") : new ArrayList<>();
//            targets.addAll(tempTarget);
//
//            List<DataEquipmentOccupancy> equipments = data.get("equipments") != null ? (List<DataEquipmentOccupancy>) data.get("equipments") : new ArrayList<>();
//
//            List<DataEquipmentOccupancy> tempEquipment = temp.get("equipments") != null ? (List<DataEquipmentOccupancy>) temp.get("equipments") : new ArrayList<>();
//            equipments.addAll(tempEquipment);
//
//            data.put("targets", tempTarget);
//            data.put("equipments", tempEquipment);
//        }

        List<DataEquipmentOccupancy> equipments = (List<DataEquipmentOccupancy>) data.get("equipments");

        // 过滤出天基平台
        List<DataGeneral> dataGenerals = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery().eq(DataGeneral::getDataType, 3));

        List<String> generalIds = dataGenerals.stream()
                .map(DataGeneral::getId)
                .collect(Collectors.toList());

        LocalDateTime targetTime = vo.getStartTime().plusSeconds(vo.getTimePoint());

        List<DataEquipmentOccupancy> satellite = equipments.stream()
                .filter(e -> generalIds.contains(e.getGeneralId()))
                .collect(Collectors.toList());

        List<SatelliteDTO> satelliteDTOList = new ArrayList<>();
        for (DataEquipmentOccupancy taskEquipmentRelation : satellite) {
            SatelliteDTO satelliteDTO = new SatelliteDTO();
            satelliteDTO.setEquipmentId(taskEquipmentRelation.getEquipmentId());
            satelliteDTO.setGeneraId(taskEquipmentRelation.getGeneralId());
            satelliteDTO.setDateTime(targetTime);

            satelliteDTOList.add(satelliteDTO);
        }
        Map<String, Map<String, Object>> coordinate = (Map<String, Map<String, Object>>) satelliteService.getSatelliteCoordinate(satelliteDTOList);
        log.info("查询卫星结果：{}", coordinate);

        return coordinate;
    }

    private Integer getTotalTime(DragParamVO dragParamVO) {
        Collection<RequirementTask> tasks = requirementTaskService.listByIds(dragParamVO.getTaskIds());
        LocalDateTime earliestStartTime = null;
        LocalDateTime latestEndTime = null;
        for (RequirementTask task : tasks) {
            if (earliestStartTime == null || task.getStartTime().isBefore(earliestStartTime)) {
                earliestStartTime = task.getStartTime();
            }
            if (latestEndTime == null || task.getEndTime().isAfter(latestEndTime)) {
                latestEndTime = task.getEndTime();
            }
        }

        dragParamVO.setStartTime(earliestStartTime);
        dragParamVO.setEndTime(latestEndTime);
        Duration between = Duration.between(earliestStartTime, latestEndTime);
        log.info("本次推演任务总时长为：{}秒", between.getSeconds());
            if (between.toDays() >= 1) {
            throw new ServiceException("所选择任务总执行时长不能大于一天");
        }
        return (int) between.getSeconds();
    }

    private List<DragReturnVO> processPlay(DragParamVO vo, Integer totalTime) {
        // 计算结束时间点
        Integer endTime = vo.getTimePoint() + vo.getStep();

        // 获取或计算当前时间点的缓存数据
        List<DragReturnVO> result = getCacheOrCompute(vo, vo.getTimePoint(), totalTime);

        if (totalTime == 0) {
            noticeClientMessage("目标数据初始化完成", 50);
            return result;
        }

        // 确定有效的结束时间点
        Integer effectiveEndTime = Math.min(endTime, totalTime);
        // 获取或计算结束时间点的缓存数据
        List<DragReturnVO> nextResult = getCacheOrCompute(vo, effectiveEndTime, totalTime);
        noticeClientMessage("正在缓存数据...", 30);

        // 计算分割片数
        Integer shard = (effectiveEndTime - vo.getTimePoint()) * SHARD_VALUE;

        // 返回切割后的结果
        return getShardResult(result, nextResult, shard);
    }

    private void noticeClientMessage(String message, Integer process) {
        Map<String, Object> msg = new HashMap<>();
        msg.put("message", message);
        msg.put("process", (double) process / 100);

        WsMessageDTO dto = new WsMessageDTO();
        dto.setData(msg);
        dto.setType(WebSocketTypeEnum.PROCESS.getCode());
        globalServer.sendAll(JSON.toJSONString(dto));
    }

    private List<DragReturnVO> processDrag(DragParamVO vo, Integer totalTime) {
        // 获取当前时间点之前所有时间点的列表
//        List<Object> timePoints = getTimePointsUpTo(vo.getTimePoint());
        // 从缓存中批量获取这些时间点的数据
//        List<Object> cachedData = RedisUtil.HashOps.hMultiGet(POINT_CACHE_PREFIX + vo.getBzId(), timePoints);
        Object value = RedisUtil.HashOps.hGet(POINT_CACHE_PREFIX + vo.getBzId(), vo.getTimePoint().toString());

        List<Object> cachedData = Arrays.asList(value);

        // 创建结果列表
        List<DragReturnVO> result = new ArrayList<>();
        // 如果缓存中没有数据，则重新计算并缓存
        if (cachedData.stream().allMatch(Objects::isNull)) {
            Map<String, String> pointMap = getPointMap(vo.getTaskIds(), totalTime);
            fillDragPoints(Arrays.asList(vo.getTimePoint()), pointMap, result);
            cachePoint(vo.getBzId(), pointMap, totalTime);
        } else {
            // 如果缓存中有数据，则直接从缓存中获取
            populateResultFromCache(cachedData, result);
        }

        return totalTime == 0 ? result : filterImportantPoints(result);
    }

    private List<DragReturnVO> getCacheOrCompute(DragParamVO vo, Integer timePoint, Integer totalTime) {
        // 获取缓存的键
        String cacheKey = POINT_CACHE_PREFIX + vo.getBzId();
        // 从缓存中获取数据
        Object cachedData = RedisUtil.HashOps.hGet(cacheKey, String.valueOf(timePoint));

        if (Objects.isNull(cachedData)) {
            // 如果缓存中没有数据，则重新计算并缓存
            Map<String, String> pointMap = getPointMap(vo.getTaskIds(), totalTime);
            cachePoint(vo.getBzId(), pointMap, totalTime);
            return JsonUtils.json2List(pointMap.get(String.valueOf(timePoint)), DragReturnVO.class);
        } else {
            // 如果缓存中有数据，则直接返回
            return JsonUtils.json2List(cachedData.toString(), DragReturnVO.class);
        }
    }

    private void cachePoint(String requirementId, Map<String, String> pointMap, Integer totalTime) {
        //将点迹数据存入redis中
        int millis = totalTime * 1000 << 1;
        RedisUtil.HashOps.hPutAll(POINT_CACHE_PREFIX + requirementId, pointMap);
        if (millis > -1) {
            RedisUtil.KeyOps.expire(POINT_CACHE_PREFIX + requirementId, millis, TimeUnit.MILLISECONDS);
        }
    }

    private Map<String, String>  getPointMap(List<String> taskIds, Integer totalTime) {
        // 根据任务查询需求
//        Collection<RequirementTask> tasks = requirementTaskService.listByIds(taskIds);
//        Map<String, Object> data = new HashMap<>();

        Map<String, Object> data = getDataByTasks(taskIds);

//        // 数据聚合
//        for (RequirementTask task : tasks) {
//            Map<String, Object> temp = getData(task.getRequirementId());
//
//            List<TaskWithTargets> targets = data.get("targets") != null ? (List<TaskWithTargets>) data.get("targets") : new ArrayList<>();
//
//            List<TaskWithTargets> tempTarget = temp.get("targets") != null ? (List<TaskWithTargets>) temp.get("targets") : new ArrayList<>();
//            targets.addAll(tempTarget);
//
//            List<DataEquipmentOccupancy> equipments = data.get("equipments") != null ? (List<DataEquipmentOccupancy>) data.get("equipments") : new ArrayList<>();
//
//            List<DataEquipmentOccupancy> tempEquipment = temp.get("equipments") != null ? (List<DataEquipmentOccupancy>) temp.get("equipments") : new ArrayList<>();
//            equipments.addAll(tempEquipment);
//
//            data.put("targets", tempTarget);
//            data.put("equipments", tempEquipment);
//        }
        List<TaskWithTargets> targets = (List<TaskWithTargets>) data.get("targets");

        // 提取航迹,并生成航迹插值点
        List<DragReturnVO> allTracks = obtainTrack(targets);

        Map<String, String> map = new HashMap<>();
        for (int i = 0; i <= totalTime; i++) {
            List<DragReturnVO> result = new ArrayList<>();
            Integer timePoint = i;
            //获取当前时间点下所有目标的点迹
            allTracks.forEach(item -> {
                DragReturnVO vo = new DragReturnVO();
                vo.setMbId(item.getMbId());
                vo.setSlhId(item.getSlhId()); //目标实例化id
                vo.setStartDate(item.getStartDate());
                vo.setType(item.getType());
                vo.setPoints(item.getPlayPoints(0, timePoint));
                vo.setIsOnlyOne(item.getIsOnlyOne());
                vo.setZbbm(item.getZbbm());
                result.add(vo);
            });
            String dragStr = JSON.toJSONString(result);
            map.put(String.valueOf(i), dragStr);
        }
        return map;
    }

    private List<DragReturnVO> obtainTrack(List<TaskWithTargets> targets) {
        // 首先需要计算出所有目标最早的起飞时间，以这个时间作为起始时间点0
        targets = calculateEarlyTime(targets);

        List<DragReturnVO> allTracks = new ArrayList<>();
        for (TaskWithTargets target : targets) {
            List<TargetWithTracks> targetWithTracks = target.getTargets();
            for (TargetWithTracks targetWithTrack : targetWithTracks) {
                List<PointVO> keyPoints;
                if (CollUtil.isEmpty(targetWithTrack.getTracks())) {
                    // 如果该目标航迹为空,则填入目标位置
                    keyPoints = handleEmptyTargetTrack(targetWithTrack.getTarget());
                } else {
                    // 根据时间对航迹进行排序
                    keyPoints = targetWithTrack.getTracks().stream()
                            .sorted(Comparator.comparing(RequirementTargetTrack::getTime))
                            .map(track -> new PointVO(track.getLongitude().doubleValue(), track.getLatitude().doubleValue(),
                                        track.getAltitude().doubleValue(), track.getTime() + target.getRelativeTime(), track.getDirect().doubleValue(),
                                    track.getSpeed().doubleValue(), true)
                            )
                            .collect(Collectors.toList());

                    // 不为0则增加一个时间点为0的关键点，此点可看为静止点
                    if (target.getRelativeTime() != 0) {
                        PointVO point = keyPoints.get(0);

                        PointVO pointVO = new PointVO();
                        BeanUtil.copyProperties(point, pointVO);
                        pointVO.setTimePoint(0);

                        keyPoints.add(pointVO);

                        keyPoints = keyPoints.stream()
                                .sorted(Comparator.comparing(PointVO::getTimePoint)).collect(Collectors.toList());
                    }
                }
                TrackVO trackVO = new TrackVO();

                trackVO.setPoints(keyPoints);
                // 获取航迹第一个点的时间即为开始时间
                trackVO.setStartTime(keyPoints.get(0).getTimePoint());

                // 生成关键点以及插值点
                List<PointVO> allPoints = generateLinePoints(trackVO);

                DragReturnVO result = new DragReturnVO();
                result.setMbId(targetWithTrack.getTarget().getTargetId());
                result.setPoints(allPoints);
                result.setStartDate(trackVO.getPoints().get(0).getTimePoint());

                allTracks.add(result);
            }
        }

        return allTracks;
    }

    private List<TaskWithTargets> calculateEarlyTime(List<TaskWithTargets> targets) {
        LocalDateTime earlyTime = null;
        for (TaskWithTargets target : targets) {
            List<TargetWithTracks> targetTargets = target.getTargets();
            TaskTargetRelation targetRelation = targetTargets.get(0).getTarget();

            if (earlyTime == null) {
                earlyTime = targetRelation.getStartTime();
            } else if (targetRelation.getStartTime().isBefore(earlyTime)) {
                earlyTime = targetRelation.getStartTime();
            }
        }

        // 計算相对时间
        for (TaskWithTargets target : targets) {
            List<TargetWithTracks> targetTargets = target.getTargets();
            TaskTargetRelation targetRelation = targetTargets.get(0).getTarget();

            Duration between = Duration.between(earlyTime, targetRelation.getStartTime());

            target.setRelativeTime((int) between.getSeconds());
        }

        // 去重
        return new ArrayList<>(targets.stream()
                .collect(Collectors.toMap(
                        TaskWithTargets::getTargetId,
                        Function.identity(),
                        (k1, k2) -> k1
                ))
                .values());
    }

    private List<PointVO> generateLinePoints(TrackVO trackVO) {
        List<PointVO> points = new ArrayList<>();
        if (trackVO.getPoints().size() > 1) {
            List<PointVO> keyPoints = trackVO.getPoints();
            //第一个关键点添加到返回结果中
            PointVO startPoint = keyPoints.get(0);
            startPoint.setPrimary(true);
            points.add(startPoint);
            try {
                for (int i = 0; i < keyPoints.size(); i++) {
                    if (i != keyPoints.size() - 1) {
                        PointVO start = keyPoints.get(i);
                        PointVO end = keyPoints.get(i + 1);
                        int seconds = end.getTimePoint() - start.getTimePoint();
                        List<PointVO> pointVos = createInterpolationPoints(start, end, trackVO.getStartTime(), seconds, trackVO);
                        points.addAll(pointVos);
                    }
                }
                //把最后一个关键点添加到返回结果中
                PointVO endPoint = keyPoints.get(keyPoints.size() - 1);
                endPoint.setPrimary(true);
                points.add(endPoint);
                log.debug("point - > size -> {}", points.size());
            } catch (Exception e) {
                log.error("{} -> TrackServiceImpl -> method generateLinePoints is failed ", JSON.toJSONString(e));
                log.error("发生异常", e);
            }
        } else {
            PointVO pointVo = trackVO.getPoints().get(0);
            pointVo.setPrimary(true);
            points = Collections.singletonList(pointVo);
        }
        return points;
    }

    private List<PointVO> createInterpolationPoints(PointVO start, PointVO end, int beginTime, int seconds, TrackVO vo) {
        List<PointVO> points = new ArrayList<>(seconds + 1);
        double startX = start.getX();
        double startY = start.getY();
        double endX = end.getX();
        double endY = end.getY();
        //起止点经度差值
        double distanceX = endX - startX;
        //起止点纬度差值
        double distanceY = endY - startY;
        //单位时间内移动的经度
        double oneUnitX = distanceX / seconds;
        //单位时间内移动的纬度
        double oneUnitY = distanceY / seconds;
        //该两点所确定直线的方位角
        double angle = BsairUtil.getPointAngle(startX, startY, endX, endY);
        //设置各关键点角度信息
        start.setAngle(angle);
        end.setAngle(angle);
        //计算速度
        double speed = getSpeed(start, end, seconds);
        //设置各关键点速度
        end.setSpeed(speed);
        start.setSpeed(speed);
        //循环生成关键点信息并添加
        for (int k = 1; k < seconds; k++) {
            beginTime++;
            if (beginTime == start.getTimePoint()) {
                start.setPrimary(true);
                points.add(start);
                vo.setStartTime(beginTime);
                k = k - 1;
                continue;
            }
            if (beginTime == end.getTimePoint()) {
                end.setPrimary(true);
                points.add(end);
                vo.setStartTime(beginTime);
                k = k - 1;
                continue;
            }
            points.add(new PointVO(startX + oneUnitX * k,
                    startY + oneUnitY * k, start.getZ(), beginTime, angle, speed, false));
        }
        vo.setStartTime(beginTime);
        return points;
    }

    private double getSpeed(PointVO start, PointVO end, int seconds) {
        Point2D.Double source = GISUtils.parsePoint2DDouble(String.valueOf(start.getX()), String.valueOf((start.getY())));
        Point2D.Double target = GISUtils.parsePoint2DDouble(String.valueOf(end.getX()), String.valueOf((end.getY())));
        double distance = GISUtils.getDistanceNew(source, target);
        return (distance * 3600) / (seconds * 1000);
    }

    private List<PointVO> handleEmptyTargetTrack(TaskTargetRelation target) {
        PointVO point = new PointVO(target.getLongitude().doubleValue(), target.getLatitude().doubleValue(), target.getAltitude().doubleValue(),
                0, 0, 0, true);
        return Collections.singletonList(point);
    }

    private List<Object> getTimePointsUpTo(Integer currentTimePoint) {
        // 获取从0到当前时间点的所有时间点列表
        return IntStream.rangeClosed(0, currentTimePoint).boxed().map(String::valueOf).collect(Collectors.toList());
    }

    private void populateResultFromCache(List<Object> cachedData, List<DragReturnVO> result) {
        // 将缓存的数据转换为对象并添加到结果列表中
        for (Object data : cachedData) {
            if (Objects.isNull(data)) continue;
            result.addAll(JsonUtils.json2List(data.toString(), DragReturnVO.class));
        }
    }

    private List<DragReturnVO> filterImportantPoints(List<DragReturnVO> points) {
        // 按目标ID分组，并将所有点合并为一个对象
        return points.stream()
                .collect(Collectors.groupingBy(DragReturnVO::getMbId))
                .values().stream()
                .map(group -> {
                    DragReturnVO result = new DragReturnVO();
                    DragReturnVO first = group.get(0);
                    BeanUtils.copyProperties(first, result);
                    result.setPoints(group.stream().flatMap(v -> v.getPoints().stream()).collect(Collectors.toList()));
                    return result;
                })
                .collect(Collectors.toList());
    }

    private void fillDragPoints(List<Object> timePoints, Map<String, String> pointMap, List<DragReturnVO> result) {
        // 根据时间点列表从点迹映射中获取数据并添加到结果列表中
        for (Object timePoint : timePoints) {
            String pointStr = pointMap.get(timePoint.toString());
            if (pointStr != null) {
                result.addAll(JsonUtils.json2List(pointStr, DragReturnVO.class));
            }
        }
    }

    private List<DragReturnVO> getShardResult(List<DragReturnVO> originResult, List<DragReturnVO> nextResult, Integer shard) {
        noticeClientMessage("目标数据初始化完成...", 50);
        return originResult.stream()
                .map(originVo -> {
                    if (shard == 0) {
                        return originVo;
                    }
                    if (originVo.getIsOnlyOne() != null && originVo.getIsOnlyOne()) {
                        return originVo;
                    }
                    // 获取原始点和下一个点的坐标
                    PointVO originPoint = originVo.getPoints().get(0);
                    DragReturnVO nextVo = nextResult.stream()
                            .filter(vo -> vo.getMbId().equals(originVo.getMbId()))
                            .findFirst()
                            .orElse(null);

                    if (nextVo == null) return originVo;

                    PointVO nextPoint = nextVo.getPoints().get(0);
                    // 计算每个分割点的经度和纬度增量
                    double xStep = (nextPoint.getX() - originPoint.getX()) / shard;
                    double yStep = (nextPoint.getY() - originPoint.getY()) / shard;

                    // 生成分割点列表
                    List<PointVO> interpolatedPoints = IntStream.range(0, shard)
                            .mapToObj(i -> new PointVO(
                                    originPoint.getX() + i * xStep,
                                    originPoint.getY() + i * yStep,
                                    originPoint.getZ(),
                                    originPoint.getTimePoint(),
                                    originPoint.getAngle(),
                                    0,
                                    null
                            ))
                            .collect(Collectors.toList());

                    DragReturnVO interpolatedVo = new DragReturnVO();
                    BeanUtils.copyProperties(originVo, interpolatedVo);
                    interpolatedVo.setPoints(interpolatedPoints);
                    return interpolatedVo;
                })
                .collect(Collectors.toList());
    }
}
