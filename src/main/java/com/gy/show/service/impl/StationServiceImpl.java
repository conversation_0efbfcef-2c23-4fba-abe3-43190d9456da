package com.gy.show.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.constants.CacheConstant;
import com.gy.show.controller.external.StationLogDTO;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.SysDataMapping;
import com.gy.show.entity.dos.SysDictionary;
import com.gy.show.entity.dto.WsMessageDTO;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.DataGeneralService;
import com.gy.show.service.FileService;
import com.gy.show.service.SysDataMappingService;
import com.gy.show.service.SysDictionaryService;
import com.gy.show.util.RedisUtil;
import com.gy.show.ws.FullViewTsServer;
import io.netty.buffer.ByteBuf;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.gy.show.constants.CacheConstant.STATION_REAL_DATA;

public class StationServiceImpl {

    @Autowired
    SysDataMappingService dataMappingService;

    @Autowired
    DataGeneralService dataGeneralService;

    @Autowired
    CommonMapper commonMapper;

    @Autowired
    FullViewTsServer server;

    @Autowired
    private FileService fileService;

    @Autowired
    private SysDictionaryService dictionaryService;

    public Map<String, Object> mappingStation(String id) {
        Map<String, Object> dataInfo;
        String info = RedisUtil.StringOps.get(CacheConstant.STATION_REAL_TIME + id);
        if (StringUtils.isBlank(info)) {
            // 查询映射关系表 2 表示航天站
            List<SysDataMapping> mapping = dataMappingService.list(Wrappers.<SysDataMapping>lambdaQuery().eq(SysDataMapping::getDataType, 2));

            Map<String, List<SysDataMapping>> keyMap = mapping.stream()
                    .collect(Collectors.groupingBy(SysDataMapping::getDataKey));

            SysDataMapping dataMapping = keyMap.get(id).get(0);

            String[] values = dataMapping.getDataValue().split("_");
            DataGeneral dataGeneral = dataGeneralService.getById(values[0]);

            // 获取图片
            String imgUrl = fileService.getImageByType(dataGeneral.getDataType());

            dataInfo = commonMapper.getOne(dataGeneral.getTableName(), values[1]);

            List<SysDictionary> sourceType = dictionaryService.getDicByType("sourceType");
            Optional<SysDictionary> first = sourceType.stream().filter(t -> t.getDictValue() == dataGeneral.getDataType()).findFirst();


            dataInfo.put("imgUrl", imgUrl);
            dataInfo.put("typeValue", dataGeneral.getTableComment());
            dataInfo.put("dataTypeValue", first.get().getDictName());
            dataInfo.put("dataType", dataGeneral.getDataType());

            // 存入缓存
            RedisUtil.StringOps.setEx(CacheConstant.STATION_REAL_TIME + id, JSON.toJSONString(dataInfo), 5, TimeUnit.SECONDS);
        } else {
            // 如果存在该缓存则刷新该缓存的过期时间，不作其它处理，后面会根据解析结果进行赋值
            RedisUtil.KeyOps.expire(CacheConstant.STATION_REAL_TIME + id, 5, TimeUnit.SECONDS);

            dataInfo = JSON.parseObject(info, Map.class);
        }

        return dataInfo;
    }
    public void sendMessage2Front(Object data, Integer type) { WsMessageDTO dto = new WsMessageDTO();
        dto.setData(data);
        dto.setType(type);

        server.sendAll(JSON.toJSONString(dto));
    }

    public void sendStationMessage2Front(ByteBuf byteBuf, String type, String message, String id) {
        // 标记当前读指针位置
        byteBuf.markReaderIndex();

        byte[] bytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(bytes);

        // 转换16进制字符串
        StringBuilder hexBuilder = new StringBuilder();
        for (byte b : bytes) {
            hexBuilder.append(String.format("%02X", b))
                    .append(" ");
        }

        // 重置读指针状态
        byteBuf.resetReaderIndex();

        StationLogDTO logDTO = new StationLogDTO();
        logDTO.setData(hexBuilder.toString());
        logDTO.setTime(DateUtil.now());
        logDTO.setType(type);
        logDTO.setMessage(message);
        Map<String, Object> stationData = getStationData(id);
        if (stationData != null) {
            logDTO.setId(stationData.get("id").toString());
        }

        // 向前端发送日志信息
        server.sendStationLog(JSON.toJSONString(logDTO));
    }

    public Map<String, Object> getStationData(String id) {
        String station = RedisUtil.StringOps.get(CacheConstant.STATION_REAL_TIME + id);

        Map<String, Object> stationMap = null;
        if (StringUtils.isNotBlank(station)) {
            stationMap = JSON.parseObject(station, new TypeReference<Map<String, Object>>(){});
        }

        return stationMap;
    }
    public void cacheStationData(Object result, String id) {
        List<Map<String, Object>> arr = (List<Map<String, Object>>) result;
        Object stationInfo = arr.get(0).get("stationInfo");
        Map<String, Object> stationJson = (Map<String, Object>) stationInfo;
        Object nodeId = stationJson.get("id");

        RedisUtil.StringOps.setEx(CacheConstant.STATION_INFO + nodeId, JSON.toJSONString(stationInfo), 30, TimeUnit.SECONDS);

        // 缓存站点数据
        RedisUtil.StringOps.setEx(STATION_REAL_DATA + id, JSON.toJSONString(result), 10, TimeUnit.SECONDS);
    }
}
