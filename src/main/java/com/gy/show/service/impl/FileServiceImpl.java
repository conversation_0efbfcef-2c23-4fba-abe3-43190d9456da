package com.gy.show.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.DataFile;
import com.gy.show.entity.dto.CoordinateDTO;
import com.gy.show.enums.DataTypeEnum;
import com.gy.show.mapper.DataFileMapper;
import com.gy.show.service.FileService;
import com.gy.show.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class FileServiceImpl extends ServiceImpl<DataFileMapper, DataFile> implements FileService {

    @Value("${file.path}")
    private String filePath;

    @Value("${local.ip}")
    private String localIp;

    @Value("${server.port}")
    private String serverPort;

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String upload(MultipartFile file, String bzPath) {
        if (bzPath == null) bzPath = "";
        StringBuilder dir = new StringBuilder(filePath)
                .append(File.separator)
                .append(bzPath)
                .append(File.separator);

        // 不存在则先创建
        File f = new File(dir.toString());
        if (!f.exists()) {
            f.mkdir();
        }

        dir.append(file.getOriginalFilename());
        FileUtil.writeFile(file, dir.toString());
        log.info("文件写入成功，文件路径：{}", dir);

        // 保存文件元数据信息
        DataFile dataFile = new DataFile();
        dataFile.setFileName(file.getOriginalFilename());
        dataFile.setFilePath(bzPath);
        dataFile.setFileSize(file.getSize());

        save(dataFile);

        return dataFile.getId();
    }

    @Override
    public String getFilePath(String fileId) {
        DataFile file = getById(fileId);

        if (file == null) {
            throw new ServiceException("文件ID不存在");
        }

        String subPath = new StringBuilder(file.getFilePath())
                .append(File.separator)
                .append(file.getFileName()).toString();

        log.info("获取文件存储路径为：{}", subPath);
        return subPath;
    }

    @Override
    public String getFileRealPath(String fileId) {
        DataFile file = getById(fileId);

        if (file == null) {
            throw new ServiceException("文件ID不存在");
        }

        // 返回真实存储路径
        String realPath = new StringBuilder(filePath)
                .append(File.separator)
                .append(file.getFilePath())
                .append(File.separator)
                .append(file.getFileName()).toString();

        log.info("获取文件真实存储路径为：{}", realPath);
        return realPath;
    }

    @Override
    public String importTrackFile(MultipartFile file) {
        // 先判断文件的后缀
        String filename = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(filename);
        if (!extension.equalsIgnoreCase("txt")) {
            throw new ServiceException("上传轨迹文件只支持txt文本格式");
        }

        // 解析文件，如果能解析成功表示文件没有问题
        try {
            parseTrackFile(file.getInputStream());
        } catch (IOException e) {
            log.error("文件解析失败");
            throw new ServiceException("文件解析失败，请传输正确的文件格式");
        }

        // 保存文件
        String fileId = upload(file, "track");

        return fileId;
    }

    @Override
    public Object loadTrackFile(String fileId) {
        DataFile dataFile = getById(fileId);
        if (dataFile == null) {
            throw new ServiceException("该文件不存在");
        }

        // 返回真实存储路径
        String realPath = new StringBuilder(filePath)
                .append(File.separator)
                .append(dataFile.getFilePath())
                .append(File.separator)
                .append(dataFile.getFileName()).toString();

        File file = new File(realPath);

        List<CoordinateDTO> coordinateDTOS;
        try {
            coordinateDTOS = parseTrackFile(new FileInputStream(file));
        } catch (IOException e) {
            log.error("文件不存在：{}", realPath);
            throw new ServiceException("文件不存在");
        }

        return coordinateDTOS;
    }

    /**
     * 根据类型获取图像url
     * @param type
     * @return
     */
    @Override
    public String getImageByType(Integer type) {
        DataTypeEnum typeEnum = DataTypeEnum.getEnumByCode(type);
        if (typeEnum == null) {
            log.error("type参数错误：{}",type);
            throw new ServiceException("type参数错误");
        }

        StringBuilder sb = new StringBuilder();
        sb.append("http://")
                .append(localIp)
                .append(":")
                .append(serverPort)
                .append(contextPath)
                .append("/file")
                .append("/image/")
                .append(typeEnum.getMessage())
                .append(".jpg");
        return sb.toString();
    }

    private List<CoordinateDTO> parseTrackFile(InputStream in) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(in, StandardCharsets.UTF_8));
        String line;
        List<CoordinateDTO> coordinateDTOList = new ArrayList<>();

        while ((line = reader.readLine()) != null) {
            // 分割数据
            String[] parts = line.trim().split("\\s+");
            if (parts.length == 3) {
                try {
                    double v = Double.parseDouble(parts[0]);
                    Double.parseDouble(parts[1]);
                    Double.parseDouble(parts[2]);

                    CoordinateDTO coordinateDTO = new CoordinateDTO();
                    coordinateDTO.setLongitude(Double.parseDouble(parts[0]));
                    coordinateDTO.setLatitude(Double.parseDouble(parts[1]));
                    coordinateDTO.setAltitude(Double.parseDouble(parts[2]));

                    coordinateDTOList.add(coordinateDTO);

//                    String key = CacheConstant.COORDINATE_FILE_PREFIX + fileId + ":" + lineNumber;
//                    RedisUtil.HashOps.hPutAll(key, coordinateMap);
//                    RedisUtil.KeyOps.expire(key, 2, TimeUnit.HOURS);
//                    lineNumber++;
                    log.info("轨迹文件解析结果：{}", line);
                } catch (NumberFormatException e) {
                    log.error("无法解析行{}", line);
                    throw new ServiceException("无法解析行" + line);
                }
            } else {
                throw new ServiceException("无效行" + line);
            }
        }

        return coordinateDTOList;
    }
}
