package com.gy.show.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.constants.Constants;
import com.gy.show.controller.external.InteractionLogDTO;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.*;
import com.gy.show.entity.dto.external.ControlHeadDTO;
import com.gy.show.entity.dto.external.ExternalRequirementDTO;
import com.gy.show.entity.dto.external.RespScheduleDTO;
import com.gy.show.entity.dto.external.StationDataDTO;
import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.enums.ExternalDataTypeEnum;
import com.gy.show.enums.TargetTypeEnum;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.runnner.NettyBootstrapRunner;
import com.gy.show.service.*;
import com.gy.show.socket.BinaryParser;
import com.gy.show.socket.message.FieldDefinition;
import com.gy.show.socket.message.HeadMessage;
import com.gy.show.socket.message.fields.*;
import com.gy.show.socket.server.UdpMulticastSender;
import com.gy.show.util.RedisUtil;
import com.gy.show.ws.FullViewTsServer;
import com.gy.show.ws.GlobalServer;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.socket.DatagramPacket;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.gy.show.constants.CacheConstant.CONTROL_TARGET_DETAIL;
import static com.gy.show.constants.CacheConstant.EXTERNAL_REQUIREMENT;
import static com.gy.show.constants.Constants.shipLineStatus;

/**
 * 主要用于处理外部数据接入与转发相关业务
 */
@Slf4j
@Service
public class ExternalDataServiceImpl implements ExternalDataService {

    @Autowired
    private NettyBootstrapRunner bootstrapRunner;

    @Autowired
    private RequirementInfoService requirementInfoService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private FullViewTsServer fullViewTsServer;

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private DataAreaService dataAreaService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private FileService fileService;

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private SysDictionaryService sysDictionaryService;

    @Autowired
    private UavStationService uavStationService;

    @Autowired
    private GlobalServer globalServer;

    /**
     * @param msg
     */
    @Override
    public void handlerBusinessData(Object msg) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();
//        UdpMulticastSender sender = getUdpSender("TODO 先写死");

        // 获取原始数组
        // 这里读取28个字节数据，最后4位是校验位
        byte[] content = new byte[28];
        byteBuf.readBytes(content);
        log.info("接收到遥控数据，数据内容长度：{}", content.length);

        // 给航天测控站发送遥控数据
        send2Space(content);

        // 给无人机测控站发送遥控数据

//        try {
//            // 转发业务数据
//            sender.sendMessage(byteBuf);
//        } catch (Exception e) {
//            log.error("业务数据转发异常", e);
//            throw new ServiceException("业务数据转发异常");
//        }

    }

    private void send2Space(byte[] content) {
        StationDataDTO stationDataDTO = new StationDataDTO();
        stationDataDTO.setContent(content);

        List<String> stationIds = Arrays.asList("_ka");
        stationDataDTO.setStationIds(stationIds);

        List<Integer> targets = Arrays.asList(0x75);
        stationDataDTO.setTargetIds(targets);

        stationDataService.remoteControlCommand(stationDataDTO);
    }

    private UdpMulticastSender getUdpSender(String type) {
        return bootstrapRunner.getSenders().get(type);
    }

    @Override
    public HeadMessage parseControlMessageHead(Object msg) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        /** head解析 总共48字节 **/
        ShortField seq = new ShortField("seq");
        ShortField source = new ShortField("source");
        ShortField sink = new ShortField("sink");
        UnSignedByteField type = new UnSignedByteField("type");
        ShortField length = new ShortField("length");
        UnSignedByteField segments = new UnSignedByteField("segments");
        UnSignedByteField segmentNum = new UnSignedByteField("segmentNum");
        FixedLengthField reserve = new FixedLengthField("reserve", 37);

        LinkedList<FieldDefinition> fields = new LinkedList<>();
        fields.addLast(seq);
        fields.addLast(source);
        fields.addLast(sink);
        fields.addLast(type);
        fields.addLast(length);
        fields.addLast(segments);
        fields.addLast(segmentNum);
        fields.addLast(reserve);

        Map<String, Object> head = BinaryParser.parseFields(byteBuf, fields);
        log.info("头部解析结果：{}", head);

        return BeanUtil.mapToBean(head, HeadMessage.class, true, CopyOptions.create().ignoreNullValue());
    }

    @Override
    public void handlerTargetData(Object msg, ExternalDataTypeEnum dataTypeEnum) {
        // 解析目标二进制数据
        List<Map<String, Object>> targetList = parseTargetData(msg);

        // 响应目标数据保存结果
        ByteBuf byteBuf = Unpooled.buffer();

        // 保存或修改目标数据
        for (Map<String, Object> targetMap : targetList) {
            String state = "01";
            Map<String, Object> copyMap = new HashMap<>();
            BeanUtil.copyProperties(targetMap, copyMap);
            try {
                saveOrUpdateTargetData(targetMap);
            } catch (Exception e) {
                log.error("目标数据保存失败", e);
                state = "02";
            }

            // 封装目标响应数据
            packageTargetResponseData(byteBuf, copyMap, state, targetList.size(), dataTypeEnum);
        }

        // 发送响应消息
        log.info("发送目标信息数据响应消息，长度：{}，data：{}", byteBuf.readableBytes(), targetList);
        UdpMulticastSender sender = getUdpSender(dataTypeEnum.getType());
        sender.sendMessage(byteBuf);
    }

    @Override
    public void handlerSituationData(Object msg, ExternalDataTypeEnum dataTypeEnum) {
        log.info("接收到实时态势数据，开始解析态势数据");
        // 解析数据
        List<Map<String, Object>> data = parseSituationData(msg, dataTypeEnum);
        
        // 数据处理，包括查询目标id
        situationDataHandler(data);

        // 向前端推送数据
        sendMessage(data);
    }

    private void situationDataHandler(List<Map<String, Object>> data) {
        for (Map<String, Object> datum : data) {
            Short tType =  (Short) datum.get("targetType");
            String generalId = TargetTypeEnum.getEnumByHexCode(FieldDefinition.byteToHex(tType.byteValue())).getCode().toString();
            String mbId = datum.get("mbId").toString();

            String targetDetailStr = RedisUtil.StringOps.get(CONTROL_TARGET_DETAIL + mbId);

            if (StringUtils.isBlank(targetDetailStr)) {
                DataGeneral dataGeneral = dataGeneralService.getById(generalId);

                // 获取图片
                String imgUrl = fileService.getImageByType(dataGeneral.getDataType());

                // 查询目标ID绑定对应的目标基础信息
                Map<String, Object> targetDetail = commonMapper.getOne(dataGeneral.getTableName(), datum.get("mbId").toString());

                if (CollUtil.isEmpty(targetDetail)) {
                    log.error("操控端推送的态势数据目标ID错误，当前推送目标ID：{} ，generalId：{}", mbId, generalId);
                } else {
                    targetDetail.put("targetTypeValue", dataGeneral.getTableComment());
                    targetDetail.put("imgUrl", imgUrl);
                }

                datum.put("bindingTarget", targetDetail);

                RedisUtil.StringOps.setEx(CONTROL_TARGET_DETAIL + mbId, JSON.toJSONString(targetDetail), 10, TimeUnit.SECONDS);
            } else {
                datum.put("bindingTarget", JSON.parseObject(targetDetailStr));

                // 刷新过期时间
                RedisUtil.KeyOps.expire(CONTROL_TARGET_DETAIL + mbId, 1, TimeUnit.SECONDS);
            }

        }
    }

    private void packageTargetResponseData(ByteBuf byteBuf, Map<String, Object> targetMap, String state, int size, ExternalDataTypeEnum dataTypeEnum) {
        ControlHeadDTO controlHeadDTO = new ControlHeadDTO();
        controlHeadDTO.setType(dataTypeEnum.getCode());
        // 头 48
        packageMessageHead(byteBuf, controlHeadDTO);

        targetResponseMessage(byteBuf, size);

        // 目标序号 1
        byte seqByte = (byte) Integer.parseInt(targetMap.get("targetSeq").toString());
        UnSignedByteField.writeField(byteBuf, seqByte);

        // 目标代号 8
        LongField.writeField(byteBuf, targetMap.get("id").toString());

        // 接收情况 1
        byte stateByte = (byte) Integer.parseInt(state, 16);
        UnSignedByteField.writeField(byteBuf, stateByte);
    }

    @Override
    public void packageScheduleResult(List<ConfirmScheduleDTO> confirmScheduleDTOs) {
        // 封装发送的数据
        ByteBuf byteBuf = packageScheduleData(confirmScheduleDTOs);

        // 给操控端发送数据
        log.info("调度结果封装完成开始发送数据到操控端, 长度：{}, data:{}", byteBuf.readableBytes(), confirmScheduleDTOs);
//        UdpMulticastSender sender = getUdpSender("这里发到哪个操控端？？");
        UdpMulticastSender sender = getUdpSender(ClientTypeEnum.PLANE.getMessage());
        sender.sendMessage(byteBuf);
    }

    @Override
    public void handlerResponseScheduleResult(Object msg) {
        log.info("接收到资源调度响应数据，开始解析");
        // 解析响应数据
        List<RespScheduleDTO> resp = parseResponseScheduleResult(msg);

        // 根据响应结果修改结果数据
//        scheduleService.handlerResponseScheduleResult(resp);
    }

    @Override
    public void handlerControlData(Object msg, ExternalDataTypeEnum dataTypeEnum) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        // 根据接口类型进行解析
        byte interfaceType = byteBuf.readByte();

        String hexType = FieldDefinition.byteToHex(interfaceType);

        switch (hexType) {
            case "00":
                // 需求信息
                handlerRequirementData(msg, dataTypeEnum);

                // 发送交互日志
                fullViewTsServer.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收到" +  dataTypeEnum.getMessage() + "发送的需求信息")));
                break;
            case "01":
                // 目标基础信息
                handlerTargetData(msg, dataTypeEnum);

                fullViewTsServer.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收到" +  dataTypeEnum.getMessage() + "发送的目标基础信息")));
                break;
            case "02":
                // 目标态势数据
                handlerSituationData(msg, dataTypeEnum);

                fullViewTsServer.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收到" +  dataTypeEnum.getMessage() + "发送的目标态势信息")));
                break;
            case "03":
                // 最终资源调度结果响应
                handlerResponseScheduleResult(msg);
                break;
        }
    }

    @Override
    public void sendBusinessData(byte[] data, ClientTypeEnum dataTypeEnum) {
        UdpMulticastSender udpSender = getUdpSender(dataTypeEnum.getMessage());

        ByteBuf buffer = Unpooled.buffer();

        ControlHeadDTO controlHeadDTO = new ControlHeadDTO();
        controlHeadDTO.setType(0x01);

        // 封装头部数据
        packageMessageHead(buffer, controlHeadDTO);

        // 业务数据
        buffer.writeBytes(data);

        udpSender.sendMessage(buffer);
    }

    @Override
    public void simulatePushBzData() {

    }

    private List<RespScheduleDTO> parseResponseScheduleResult(Object msg) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        LinkedList<FieldDefinition> taskSeqList = new LinkedList<>();

        // 任务数量 2
        ShortField taskNum = new ShortField("taskNum");
        taskSeqList.add(taskNum);

        Map<String, Object> taskSeqMap = BinaryParser.parseFields(byteBuf, taskSeqList);
        int num = Integer.parseInt(taskSeqMap.get("taskNum").toString());

        List<RespScheduleDTO> resp = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            LinkedList<FieldDefinition> result = new LinkedList<>();
            // 任务序号 2
            ShortField taskSeq = new ShortField("taskSeq");
            result.add(taskSeq);

            // 节点ID 8
            LongField equipmentId = new LongField("equipmentId");
            result.add(equipmentId);

            // 需求ID 8
            LongField requirementId = new LongField("requirementId");
            result.add(requirementId);

            // 任务ID 8
            LongField taskId = new LongField("taskId");
            result.add(taskId);

            // 目标ID 8
            LongField targetId = new LongField("targetId");
            result.add(targetId);

            // 业务类型 1
            UnSignedByteField bzType = new UnSignedByteField("bzType");
            result.add(bzType);

            // 接收情况 1
            UnSignedByteField state = new UnSignedByteField("state");
            result.add(state);

            // 预留 16
            FixedLengthField reverse = new FixedLengthField("reverse", 16);
            result.add(reverse);

            Map<String, Object> resultMap = BinaryParser.parseFields(byteBuf, result);
            log.info("资源调度结果任务解析结果：{}", resultMap);

            // 转换成实体类
            RespScheduleDTO requirementInfoDTO = BeanUtil.mapToBean(resultMap, RespScheduleDTO.class, true, CopyOptions.create().ignoreNullValue());
            resp.add(requirementInfoDTO);
        }

        return resp;
    }

    private ByteBuf packageScheduleData(List<ConfirmScheduleDTO> confirmScheduleDTOs) {
        ByteBuf byteBuf = Unpooled.buffer();

        ControlHeadDTO controlHeadDTO = new ControlHeadDTO();
        // TODO 这里先写死，后面需要根据调度的设备去发送对应的包类型
        controlHeadDTO.setType(0x04);
        // 报文头
        packageMessageHead(byteBuf, controlHeadDTO);

        // 接口类型 1
        byte type = (byte) Integer.parseInt("02", 16);
        UnSignedByteField.writeField(byteBuf, type);

        // 任务数量 2
        ShortField.writeField(byteBuf, confirmScheduleDTOs.size());

        for (int i = 0; i < confirmScheduleDTOs.size(); i++) {
            ConfirmScheduleDTO confirmScheduleDTO = confirmScheduleDTOs.get(i);

            //  查询任务
            RequirementTask task = requirementTaskService.getById(confirmScheduleDTO.getTaskId());

            // 任务序号 2
            ShortField.writeField(byteBuf, i);

            // 所属需求 2 -> 8
//            LongField.writeField(byteBuf, "123");
            LongField.writeField(byteBuf, task.getRequirementId());

            // 任务名称 ID? 2 -> 8
            LongField.writeField(byteBuf, confirmScheduleDTO.getTaskId());

            // 目标代号 8 暂时无用
            LongField.writeField(byteBuf, "1111");

            // 业务类型 1
//            UnSignedByteField.writeField(byteBuf, new Integer(1).byteValue());
            UnSignedByteField.writeField(byteBuf, task.getTaskType().byteValue());

            // 使用资源总数 2
            List<ConfirmScheduleDTO.EquipmentDTO> equipments = confirmScheduleDTO.getEquipments();
            ShortField.writeField(byteBuf, equipments.size());

            // 循环任务使用资源
            int num = 0;
            for (ConfirmScheduleDTO.EquipmentDTO equipment : equipments) {
                // 资源窗口序号 2
                ShortField.writeField(byteBuf, num);

                // 节点ID 8
                LongField.writeField(byteBuf, equipment.getEquipmentId());

                // 资源使用开始时间 7
                DateField.writeField(byteBuf, equipment.getStartTime());

                // 资源使用结束时间 7
                DateField.writeField(byteBuf, equipment.getEndTime());

                // 预留字段
                byteBuf.writeBytes(new byte[32]);
            }
        }
        
        return byteBuf;
    }

    private void sendMessage(List<Map<String, Object>> data) {
        WsMessageDTO dto = new WsMessageDTO();
        dto.setData(data);
        dto.setType(WebSocketTypeEnum.FULL_VIEW_TARGET.getCode());
        fullViewTsServer.sendAll(JSON.toJSONString(dto));
    }

    private List<Map<String, Object>> parseSituationData(Object msg, ExternalDataTypeEnum dataTypeEnum) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        // 目标数量 1
        UnSignedByteField targetCount = new UnSignedByteField("targetCount");
        LinkedList<FieldDefinition> targets = new LinkedList<>();
        targets.addLast(targetCount);

        Map<String, Object> typeMap = BinaryParser.parseFields(byteBuf, targets);
        Integer targetC = Integer.parseInt(typeMap.get("targetCount").toString());
        log.info("本次态势数据传输目标数量为：{}", targetC);

        List<Map<String, Object>> result = new ArrayList<>();
        for (int i = 0; i < targetC; i++) {
            LinkedList<FieldDefinition> positionList = new LinkedList<>();
            // 目标序号 1
            UnSignedByteField seq = new UnSignedByteField("seq");
            positionList.add(seq);

            // 目标ID 8
            LongField mbId = new LongField("mbId");
            positionList.add(mbId);

            // 目标名称 20
            FixedLengthField name = new FixedLengthField("name", 20);
            positionList.add(name);

            // 目标类别 1 0x00：无人车
            //0x01：无人艇
            //0x02：无人机
            UnSignedByteField targetType = new UnSignedByteField("targetType");
            positionList.add(targetType);

            // 不同类型的操控端态势数据不同
            switch (dataTypeEnum) {
                case CAR_DATA: {
                    // 无人车
                    log.info("开始解析无人车态势数据");
                    carSituationData(positionList);
                    break;
                }
                case SHIP_DATA: {
                    // 无人艇
                    log.info("开始解析无人艇态势数据");
                    shipSituationData(positionList);
                    break;
                }
                case PLANE_DATA: {
                    // 无人机
                    log.info("开始解析无人机态势数据");
                    uavSituationData(positionList);
                    break;
                }
            }

            // 执行任务数量 2
            ShortField taskCount = new ShortField("taskCount");
            positionList.add(taskCount);

            // 解析数据任务数据
            Map<String, Object> positionMap = BinaryParser.parseFields(byteBuf, positionList);

            // 单独处理byte数组
            byte[] nam = (byte[]) positionMap.get("name");
            positionMap.put("name", new String(nam, StandardCharsets.UTF_8).trim());

            Integer taskC = (Integer) positionMap.get("taskCount");
            log.info("目标态势数据解析结果：{}, 任务数量：{}", positionMap, taskC);

            for (int j = 0; j < taskC; j++) {
                LinkedList<FieldDefinition> taskList = new LinkedList<>();
                // 执行任务序号 2
                ShortField taskSeq = new ShortField("taskSeq");
                taskList.add(taskSeq);

                // 需求ID 8
                LongField requirementId = new LongField("requirementId");
                taskList.add(requirementId);

                // 任务ID 8
                LongField taskId = new LongField("taskId");
                taskList.add(taskId);

                // 任务名称
                FixedLengthField taskName = new FixedLengthField("taskName", 20);
                taskList.add(taskName);

//                // 接入节点代号 8
//                LongField equipmentId = new LongField("equipmentId");
//                taskList.add(equipmentId);

                // 任务执行进度 2
                DoubleField process = new DoubleField("process", 100);
                taskList.add(process);

                // 解析任务数据结果
                Map<String, Object> taskMap = BinaryParser.parseFields(byteBuf, taskList);

                taskMap.put("taskName", new String((byte[]) taskMap.get("taskName")).trim());
                log.info("解析任务数据结果：{}", taskMap);

                ((List) positionMap.computeIfAbsent("tasks", k -> new ArrayList<>())).add(taskMap);
            }

            // 预留 16
            FixedLengthField reverse = new FixedLengthField("reverse", 16);

            Map<String, Object> reverseMap = BinaryParser.parseFields(byteBuf, Arrays.asList(reverse));
            positionMap.putAll(reverseMap);

            result.add(positionMap);
        }
        // 心跳计数
        byte heart = byteBuf.readByte();
        log.info("心跳计数：{}", heart);

        return result;
    }

    private void uavSituationData(LinkedList<FieldDefinition> positionList) {
        // 相对高度 4
        UavPositionField xdgd = new UavPositionField("xdgd");
        positionList.add(xdgd);

        // GPS高度 4
        UavPositionField gpsgd = new UavPositionField("gpsgd");
        positionList.add(gpsgd);

        // 空速 4
        UavPositionField ks = new UavPositionField("ks");
        positionList.add(ks);

        // 地速 4
        UavPositionField ds = new UavPositionField("ds");
        positionList.add(ds);

        // 经度 4
        PositionField longitude = new PositionField("longitude");
        positionList.add(longitude);

        // 纬度 4
        PositionField latitude = new PositionField("latitude");
        positionList.add(latitude);

        // 俯仰 4
        UavPositionField fy = new UavPositionField("fy");
        positionList.add(fy);

        // 滚转 4
        UavPositionField gz = new UavPositionField("gz");
        positionList.add(gz);

        // 偏航 4
        UavPositionField ph = new UavPositionField("ph");
        positionList.add(ph);

        // 链路连接状态 1
        SingleBitField lineStatus = new SingleBitField("shipLineStatus", shipLineStatus);
        positionList.add(lineStatus);
    }

    private void shipSituationData(LinkedList<FieldDefinition> positionList) {
        // 经度 4
        PositionField longitude = new PositionField("longitude");
        positionList.add(longitude);

        // 纬度 4
        PositionField latitude = new PositionField("latitude");
        positionList.add(latitude);

        // 横摇 4
        AngleField hy = new AngleField("hy");
        positionList.add(hy);

        // 纵摇
        AngleField zy = new AngleField("zy");
        positionList.add(zy);

        // 艏相角
        AngleField sxj = new AngleField("sxj");
        positionList.add(sxj);

        // 速度方向 4
        AngleField sdfx = new AngleField("sdfx");
        positionList.add(sdfx);

        // 速度大小 4
        AngleField sddx = new AngleField("sddx");
        positionList.add(sddx);

        // 时间 8
        LongField sj = new LongField("sj");
        positionList.add(sj);

        // 舵角 4
        RudderAngleField dj = new RudderAngleField("dj");
        positionList.add(dj);

        // 控制权 1
        UnSignedByteField kzq = new UnSignedByteField("kzq");
        positionList.add(kzq);

        // 控制模式 1
        UnSignedByteField kzms = new UnSignedByteField("kzms");
        positionList.add(kzms);

        // 惯导模式 4
        IntegerField gdmx = new IntegerField("gdmx");
        positionList.add(gdmx);

        // 链路连接状态 1
        SingleBitField lineStatus = new SingleBitField("shipLineStatus", shipLineStatus);
        positionList.add(lineStatus);
    }

    private void carSituationData(LinkedList<FieldDefinition> positionList) {
        // 俯仰角 1
        UnSignedByteField pitchAngle = new UnSignedByteField("pitchAngle");
        positionList.add(pitchAngle);

        // 侧倾角 1
        UnSignedByteField rollAngle = new UnSignedByteField("rollAngle");
        positionList.add(rollAngle);

        // 航向角 2
        ShortField headAngle = new ShortField("headAngle");
        positionList.add(headAngle);

        // 经度 4
        PositionField longitude = new PositionField("longitude");
        positionList.add(longitude);

        // 纬度 4
        PositionField latitude = new PositionField("latitude");
        positionList.add(latitude);

        // 跟踪偏差 2
        ShortField tracking = new ShortField("tracking");
        positionList.add(tracking);

        // 速度 4
        IntegerField speed = new IntegerField("speed");
        positionList.add(speed);

        // 目标跟踪状态 1
        CharField trackState = new CharField("trackState", Constants.targetTrackState);
        positionList.add(trackState);

        // 单次里程 4
        IntegerField singleDistance = new IntegerField("singleDistance");
        positionList.add(singleDistance);

        // 超声波检测距离 4
        IntegerField csbDistance = new IntegerField("csbDistance");
        positionList.add(csbDistance);

        // 平台机动状态 1
        MultiBitField platformState = new MultiBitField("platformState", 0);
//            UnSignedByteField platformState = new UnSignedByteField("platformState");
        positionList.add(platformState);

        // 车速 2
        ShortField carSpeed = new ShortField("carSpeed");
        positionList.add(carSpeed);

        // 系统电压（电池状态电压V） 1
        UnSignedByteField voltage = new UnSignedByteField("voltage");
        positionList.add(voltage);

        // 电池Soc 1
        UnSignedByteField charge = new UnSignedByteField("charge");
        positionList.add(charge);

        // 电池电流 2
        ShortField electric = new ShortField("electric");
        positionList.add(electric);

        // 发电机电流 1
        UnSignedByteField generatorCurrent = new UnSignedByteField("generatorCurrent");
        positionList.add(generatorCurrent);

        // 发动机转速 1
        UnSignedByteField rpm = new UnSignedByteField("rpm");
        positionList.add(rpm);

        // 底盘状态 1
        CharField chassisState = new CharField("chassisState", Constants.chassisState);
        positionList.add(chassisState);

        // 底盘各部分状态 1
        UnSingleBitField chassisMultiState = new UnSingleBitField("chassisMultiState", Constants.chassisMultiState);
        positionList.add(chassisMultiState);

        // 油量 1
        UnSignedByteField oil = new UnSignedByteField("oil");
        positionList.add(oil);

        // 车辆工作模式 1
        SingleBitField carMode = new SingleBitField("carMode", Constants.carWorkMode);
        positionList.add(carMode);

        // 链路连接状态 1
        SingleBitField lineStatus = new SingleBitField("lineStatus", shipLineStatus);
        positionList.add(lineStatus);
    }

    private void targetResponseMessage(ByteBuf byteBuf, Integer count) {
        // 接口类型 1
        byte type = (byte) Integer.parseInt("01", 16);
        UnSignedByteField.writeField(byteBuf, type);

        // 目标数量 1
        UnSignedByteField.writeField(byteBuf, count.byteValue());

    }

    private void saveOrUpdateTargetData(Map<String, Object> target) {
        Short targetType = (Short) target.get("targetCategory");

        // 这里需要根据类型查询具体的id
        TargetTypeEnum targetTypeEnum = TargetTypeEnum.getEnumByHexCode(FieldDefinition.byteToHex(targetType.byteValue()));
        DataGeneral dataGeneral = dataGeneralService.getById(targetTypeEnum.getCode());

        BasicDataDTO dataDTO = new BasicDataDTO();
        dataDTO.setDataTypeId(dataGeneral.getId());
        dataDTO.setDataId(target.get("id").toString());

        // 多选字段转换
        String targetFrequency = fieldConvert((Map<String, Object>) target.get("frequency"));
        target.put("frequency", targetFrequency);

        String business = fieldConvert((Map<String, Object>) target.get("businessType"));
        target.put("businessType", business);

        String workSystem = fieldConvert((Map<String, Object>) target.get("workSystem"));
        target.put("workSystem", workSystem);

        // 根据area查询areaId 地基就直接按名字去查询
        String areaName = target.get("area").toString();
        List<DataArea> dataAreas = dataAreaService.list(Wrappers.<DataArea>lambdaQuery().eq(DataArea::getAreaType, 2)
                .eq(DataArea::getAreaName, areaName));

        if (CollUtil.isNotEmpty(dataAreas)) {
            target.put("areaId", dataAreas.get(0).getId());
        }

        // 删除不需要的字段
        target.remove("maxAngel");
        target.remove("minAngel");
        target.remove("maxAzimuth");
        target.remove("minAzimuth");
        target.remove("reverse");
        target.remove("targetSeq");
        target.remove("targetCategory");
        target.remove("area");
        target.remove("controlRange");
        target.remove("targetType");


        // 将字典类型字段映射到自己的数据库中
        mappingField(target, "antennaType");
        mappingField(target, "workSystem");
        mappingField(target, "frequency");

        target.put("code", target.get("code") == null ? target.get("name") : target.get("code"));

        dataDTO.setData(target);


        // 保存目标基础数据
        dataGeneralService.saveData(dataDTO);
    }

    private void mappingField(Map<String, Object> target, String type) {
        List<SysDictionary> dic = sysDictionaryService.getDicByType(type);
        Map<String, List<SysDictionary>> antennaMap = dic.stream()
                .collect(Collectors.groupingBy(SysDictionary::getDictName));

        String s = target.get(type).toString();
        String[] strs = s.split(",");

        List<Integer> value = new ArrayList<>();
        for (String str : strs) {
            Integer dictValue = antennaMap.get(str).get(0).getDictValue();

            value.add(dictValue);
        }
        target.put(type, CollUtil.join(value, ","));
    }

    private String fieldConvert(Map<String, Object> targetMap) {
        List<String> strList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : targetMap.entrySet()) {
            boolean isTrue = (boolean) entry.getValue();
            if (isTrue) strList.add(entry.getKey());
        }
        return CollUtil.join(strList, ",");
    }

    /**
     * 解析目标基础数据
     * @param msg
     */
    private List<Map<String, Object>> parseTargetData(Object msg) {
        log.info("接收到目标信息基础数据，准备开始解析");
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        // 目标数量 1
        UnSignedByteField targetCount = new UnSignedByteField("targetCount");
        LinkedList<FieldDefinition> targets = new LinkedList<>();
        targets.addLast(targetCount);

        Map<String, Object> typeMap = BinaryParser.parseFields(byteBuf, targets);
        Integer targetC = Integer.parseInt(typeMap.get("targetCount").toString());
        log.info("本次传输目标数量为：{}", targetC);

        List<Map<String, Object>> result = new ArrayList<>();
        for (int i = 0; i < targetC; i++) {
            LinkedList<FieldDefinition> targetList = new LinkedList<>();
            // 目标序号 1
            UnSignedByteField targetSeq = new UnSignedByteField("targetSeq");
            targetList.add(targetSeq);

            // 目标名称 20
            FixedLengthField targetName = new FixedLengthField("name", 20);
            targetList.add(targetName);

            // 目标ID 8
            LongField id = new LongField("id");
            targetList.add(id);

            // 所属域 1
            CharField area = new CharField("area", Constants.area);
            targetList.add(area);

            // 目标类别 1
            UnSignedByteField targetCategory = new UnSignedByteField("targetCategory");
            targetList.add(targetCategory);

            // 目标类型 1
            UnSignedByteField targetType = new UnSignedByteField("targetType");
            targetList.add(targetType);

            // 可支持频段 1 入库保存时需要做转换
            SingleBitField targetFrequency = new SingleBitField("frequency", Constants.targetFrequency);
            targetList.add(targetFrequency);

            // 可支持业务类型 入库保存时需要做转换
            SingleBitField business = new SingleBitField("businessType", Constants.taskTypeConvert);
            targetList.add(business);

            // EIRP 2
            ShortField eirp = new ShortField("eirp");
            targetList.add(eirp);

            // G/T 2
            ShortField gt = new ShortField("gt");
            targetList.add(gt);

            // 工作体制 1  入库保存时需要做转换
            SingleBitField workSystem = new SingleBitField("workSystem", Constants.workSystem);
            targetList.add(workSystem);

            // 天线类型 1
            CharField antennaType = new CharField("antennaType", Constants.antennaType);
            targetList.add(antennaType);

            // 测控方位角范围 最小值 2
            ShortField minAzimuth = new ShortField("minAzimuth");
            targetList.add(minAzimuth);

            // 测控方位角范围 最大值 2
            ShortField maxAzimuth = new ShortField("maxAzimuth");
            targetList.add(maxAzimuth);

            // 测控俯仰角范围 最小值 2
            ShortField minAngel = new ShortField("minAngel");
            targetList.add(minAngel);

            // 测控方位角范围 最大值 2
            ShortField maxAngel = new ShortField("maxAngel");
            targetList.add(maxAngel);

            // 测控距离 4
            IntegerField controlRange = new IntegerField("controlRange");
            targetList.add(controlRange);

            // 预留 32
            FixedLengthField reverse = new FixedLengthField("reverse", 32);
            targetList.add(reverse);

            Map<String, Object> targetMap = BinaryParser.parseFields(byteBuf, targetList);

            targetMap.put("name", new String((byte[]) targetMap.get("name"), StandardCharsets.UTF_8).trim());
            log.info("目标基础数据解析结果：{}", targetMap);
            result.add(targetMap);
        }

        return result;
    }



    /**
     * 管控数据
     * 0x01：已受理
     * 0x02：已拒绝
     * 0x03：解析报错（格式问题无法解析）
     * 0x04：校验未通过
     */
    @Override
    public void handlerRequirementData(Object msg, ExternalDataTypeEnum dataTypeEnum) {
        String status = "01";
        RequirementInfoDTO data = new RequirementInfoDTO();
        try {
            // 解析二进制数据
            data = parseRequirementData(msg);
        } catch (Exception e) {
            log.error("解析失败，原因", e);
            status = "03";
        }

        /** 2025.06.25 update 这里先不保存，存一条消息到redis中，用户手动点击需求确认 **/
        cacheRequirementRequest(msg, dataTypeEnum, data);
//        try {
//            data.setIsDeCompose(1);
//            // 新增需求
//            requirementInfoService.addRequirementInfo(data);
//        } catch (Exception e) {
//            log.error("入库保存失败，请检查参数是否正确", e);
//            status = "04";
//        }

        ByteBuf byteBuf = Unpooled.buffer();
        data.setHandleStatus(status);

        // 封装响应消息 TODO 测试暂时注释
//        requirementResponseMessage(byteBuf, data, dataTypeEnum);

        // 发送响应消息
        log.info("发送udp响应数据，数据长度：{}, data：{}", byteBuf.readableBytes(), data);
        UdpMulticastSender sender = getUdpSender(dataTypeEnum.getType());
        sender.sendMessage(byteBuf);
    }

    private void cacheRequirementRequest(Object msg, ExternalDataTypeEnum dataTypeEnum, RequirementInfoDTO data) {
        DatagramPacket packet = (DatagramPacket) msg;
        InetSocketAddress sender = packet.sender();

        // 复制基础属性
        ExternalRequirementDTO requirementDTO = new ExternalRequirementDTO();
        BeanUtil.copyProperties(data, requirementDTO);

        requirementDTO.setDataSource(dataTypeEnum.getMessage());
        requirementDTO.setHost(sender.getAddress().getHostAddress());
        requirementDTO.setPort(sender.getPort());
        requirementDTO.setRequestTime(DateUtil.now());

        // 生成一个ID便于后面用户选择的查询
        requirementDTO.setId(IdWorker.getIdStr());

        Map<String, Object> objectMap = BeanUtil.beanToMap(requirementDTO);
        objectMap.put("startTime", DateUtil.formatLocalDateTime(requirementDTO.getStartTime()));
        objectMap.put("endTime", DateUtil.formatLocalDateTime(requirementDTO.getEndTime()));

        Map<String, String> stringMap = new HashMap<>();
        objectMap.forEach((key, value) -> {
            if (value != null) {
                String v;
                if (key.equalsIgnoreCase("targetInfos")) {
                    v = JSON.toJSONString(value);
                } else {
                    v = value.toString();
                }
                stringMap.put(key, v);
            }
        });

        // 存入redis缓存中
        RedisUtil.HashOps.hPutAll(EXTERNAL_REQUIREMENT + requirementDTO.getId(), stringMap);

        // 推送消息到前端
        WsMessageDTO messageDTO = new WsMessageDTO();
        messageDTO.setData(stringMap);
        messageDTO.setType(WebSocketTypeEnum.FILE.getCode());

        globalServer.sendAll(JSON.toJSONString(messageDTO));

//        // 存入redis缓存中
//        String exist = RedisUtil.StringOps.get(EXTERNAL_REQUIREMENT);
//
//        if (StringUtils.isNotBlank(exist)) {
//            // 如果存在则添加这个请求
//            List<ExternalRequirementDTO> requirementDTOS = JSON.parseArray(exist, ExternalRequirementDTO.class);
//
//            requirementDTOS.add(requirementDTO);
//
//            RedisUtil.StringOps.set(EXTERNAL_REQUIREMENT, JSON.toJSONString(requirementDTOS));
//        } else {
//            // 不存在则直接添加
//            RedisUtil.StringOps.set(EXTERNAL_REQUIREMENT, JSON.toJSONString(Collections.singletonList(requirementDTO)));
//        }
    }

    /**
     * 报文头
     * @param byteBuf
     * @param controlHeadDTO
     */
    private void packageMessageHead(ByteBuf byteBuf, ControlHeadDTO controlHeadDTO) {
        byteBuf.writeShort(1);
        byteBuf.writeShort(123);
        byteBuf.writeShort(8000);
        byteBuf.writeByte(controlHeadDTO.getType());
        byteBuf.writeShort(60);
        byteBuf.writeByte(1);
        byteBuf.writeByte(1);
        byteBuf.writeBytes(new byte[37]);
    }

    public static void main(String[] args) {
//        RequirementInfoDTO data = new RequirementInfoDTO();
//        data.setId("123123");
//        data.setRequirementType(1);
//        data.setImportance(1);
//        data.setStartTime(LocalDateTime.now());
//        data.setEndTime(LocalDateTime.now());
//
//        ByteBuf byteBuf = Unpooled.buffer();
//        data.setHandleStatus("1");

        String str = "2024-10-29 02:12:00";

        LocalDateTime localDateTime = DateUtil.parseLocalDateTime(str);

        System.out.println(localDateTime.getSecond());

        // 封装响应消息
//        ExternalDataServiceImpl externalDataService = new ExternalDataServiceImpl();
//        externalDataService.requirementResponseMessage(byteBuf, data);
////        externalDataService.packageMessageHead(byteBuf);
//
//        log.info("发送udp响应数据，数据长度：{}", byteBuf.readableBytes());
////        UdpMulticastSender sender = bootstrapRunner.getSender();
////        sender.sendMessage(byteBuf);
    }

    private void requirementResponseMessage(ByteBuf byteBuf, RequirementInfoDTO data, ExternalDataTypeEnum dataTypeEnum) {
        ControlHeadDTO controlHeadDTO = new ControlHeadDTO();
        controlHeadDTO.setType(dataTypeEnum.getCode());
        // 报文头
        packageMessageHead(byteBuf, controlHeadDTO);

        // 接口类型
        byte type = (byte) Integer.parseInt("00", 16);
        UnSignedByteField.writeField(byteBuf, type); // 0x00:需求响应 0x01: 目标基础信息响应 0x02: 资源调度最终结果

        // 需求数量
        ShortField.writeField(byteBuf, 1); // 先默认为1
//        byteBuf.writeShort(1);

        // 需求序号
        ShortField.writeField(byteBuf, 1); // 先默认为1
//        byteBuf.writeShort(1);

        // 需求ID
        LongField.writeField(byteBuf, data.getId());

        // 需求类型
        UnSignedByteField.writeField(byteBuf, data.getRequirementType().byteValue());

        // 重要程度
        UnSignedByteField.writeField(byteBuf, data.getImportance().byteValue());

        // 需求开始时间
        DateField.writeField(byteBuf, data.getStartTime());

        // 需求结束时间
        DateField.writeField(byteBuf, data.getEndTime());

        // 需求受理情况
        byte status = (byte) Integer.parseInt(data.getHandleStatus(), 16);
        UnSignedByteField.writeField(byteBuf, status);

        // 预留
        byteBuf.writeBytes(new byte[16]);

    }

    /**
     * 解析需求信息
     * @param msg
     */
    private RequirementInfoDTO parseRequirementData(Object msg) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        // 需求总数 2
        ShortField requirementCount = new ShortField("requirementCount");
        // 需求序号 2
        ShortField requirementSeq = new ShortField("requirementSeq");
        // 需求ID 2
//        ShortField id = new ShortField("id");
        // 需求名称 20
        FixedLengthField requirementName = new FixedLengthField("requirementName", 20);
        // 需求类型 1
        UnSignedByteField requirementType = new UnSignedByteField("requirementType");
        // 重要程度 1
        UnSignedByteField importance = new UnSignedByteField("importance");
        // 需求开始时间 7
        DateField startTime = new DateField("startTime");
        // 需求结束时间 7
        // 需求描述 60
        DateField endTime = new DateField("endTime");
        FixedLengthField requirementComment = new FixedLengthField("requirementComment", 60);

        LinkedList<FieldDefinition> requirements = new LinkedList<>();
        requirements.addLast(requirementCount);
        requirements.addLast(requirementSeq);
//        requirements.addLast(id);
        requirements.addLast(requirementName);
        requirements.addLast(requirementType);
        requirements.addLast(importance);
        requirements.addLast(startTime);
        requirements.addLast(endTime);
        requirements.addLast(requirementComment);

        /** 解析需求信息 start **/
        Map<String, Object> requirement = BinaryParser.parseFields(byteBuf, requirements);
        byte[] bytes = (byte[]) requirement.get("requirementName");
        requirement.put("requirementName", new String(bytes, StandardCharsets.UTF_8).trim());

        byte[] requirementCommentBytes = (byte[]) requirement.get("requirementComment");
        requirement.put("requirementComment", new String(requirementCommentBytes, StandardCharsets.UTF_8).trim());

        log.info("需求信息解析结果：{}", requirement);
        // 转换成实体类
        RequirementInfoDTO requirementInfoDTO = BeanUtil.mapToBean(requirement, RequirementInfoDTO.class, true, CopyOptions.create().ignoreNullValue());
        log.info("需求实体类：{}", requirementInfoDTO);

        // 目标数量 2
        ShortField targetCount = new ShortField("targetCount");

        Map<String, Object> countMap = BinaryParser.parseFields(byteBuf, Arrays.asList(targetCount));
        Integer count = Integer.parseInt(countMap.get("targetCount").toString());
        log.info("需求关联目标数量为：{}", count);

        List<RequirementInfoDTO.TargetInfo> targetInfos = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            LinkedList<FieldDefinition> target = new LinkedList<>();
            // 目标序号 2
            ShortField targetSeq = new ShortField("targetSeq");
            // 目标ID 8
            LongField targetId = new LongField("targetId");
            // 目标类型 1
            UnSignedByteField targetType = new UnSignedByteField("targetType");
            // 所属域 1
            UnSignedByteField area = new UnSignedByteField("area");
            // 轨迹开始时间 7
            DateField trackStartTime = new DateField("trackStartTime");
            // 轨迹结束时间 7
            DateField trackEndTime = new DateField("trackEndTime");

            /** 解析目标信息 **/
            target.addLast(targetSeq);
            target.addLast(targetId);
            target.addLast(targetType);
            target.addLast(area);
            target.addLast(trackStartTime);
            target.addLast(trackEndTime);

            Map<String, Object> targetMap = BinaryParser.parseFields(byteBuf, target);
            log.info("目标信息解析结果：{}", targetMap);
            // 转换实体类
            RequirementInfoDTO.TargetInfo targetInfo = BeanUtil.mapToBean(targetMap, RequirementInfoDTO.TargetInfo.class,
                    true, CopyOptions.create().ignoreNullValue());
            // 目标类型转换
            Short tType =  (Short) targetMap.get("targetType");
            targetInfo.setGeneralId(TargetTypeEnum.getEnumByHexCode(targetType.byteToHex(tType.byteValue())).getCode().toString());
            log.info("目标实体类：{}", targetInfo);

            /** 循环解析轨迹信息 **/
            LinkedList<FieldDefinition> trackList = new LinkedList<>();
            // 轨迹名称 20
            FixedLengthField trackName = new FixedLengthField("trackName", 20);
            // 坐标点数 4
            IntegerField positionCount = new IntegerField("positionCount");

            trackList.addLast(trackName);
            trackList.addLast(positionCount);

            // 解析航迹点数有多少
            Map<String, Object> trackInfo = BinaryParser.parseFields(byteBuf, trackList);
            Integer pCount = Integer.parseInt(trackInfo.get("positionCount").toString());
//            trackInfo.put("trackName", new String((byte[]) trackInfo.get("trackName"), StandardCharsets.UTF_8).trim());

            byte[] trackNameBytes = (byte[]) trackInfo.get("trackName");
            String tName = new String(trackNameBytes, StandardCharsets.UTF_8).trim();
            trackInfo.put("trackName", tName);

            log.info("轨迹信息：{}", trackInfo);
            log.info("目标名称：{},目标航迹点数量为：{}", tName, pCount);

            List<DataPresetTrack> tracks = new LinkedList<>();
            /** 解析坐标点 start **/
            for (int j = 0; j < pCount; j++) {
                LinkedList<FieldDefinition> points = new LinkedList<>();
                // 坐标序号 4
                IntegerField positionSeq = new IntegerField("positionSeq");
                points.add(positionSeq);
                // 经度 4
                PositionField longitude = new PositionField("longitude");
                points.add(longitude);
                // 纬度 4
                PositionField latitude = new PositionField("latitude");
                points.add(latitude);
                // 高度 4
                IntegerField altitude = new IntegerField("altitude");
                points.add(altitude);
                // 速度 4
                IntegerField speed = new IntegerField("speed");
                points.add(speed);
                // 预留 4
                FixedLengthField reserve = new FixedLengthField("reserve", 4);
                points.add(reserve);

                Map<String, Object> pointMap = BinaryParser.parseFields(byteBuf, points);

                String spd = ((Integer) pointMap.get("speed")).toString();
                pointMap.put("speed", Double.parseDouble(spd) / 100);
                log.info("目标名称：{}, 坐标点解析结果为：{}", tName, pointMap);

                DataPresetTrack dataPresetTrack = BeanUtil.mapToBean(pointMap, DataPresetTrack.class,
                        true, CopyOptions.create().ignoreNullValue());

                tracks.add(dataPresetTrack);
            }
            /** 解析坐标点 end **/

            /** 解析任务 start **/
            // 执行任务信息条数 2
            ShortField executeCount = new ShortField("executeCount");
            Map<String, Object> executeC = BinaryParser.parseFields(byteBuf, Arrays.asList(executeCount));
            Integer executeCou = Integer.parseInt(executeC.get("executeCount").toString());

            log.info("目标名称：{}，目标任务数量为：{}", tName, executeCou);

            List<RequirementTaskDTO> tasks = new LinkedList<>();
            for (int j = 0; j < executeCou; j++) {
                LinkedList<FieldDefinition> taskList = new LinkedList<>();
                // 任务ID 2
                ShortField taskId = new ShortField("taskId");
                taskList.add(taskId);

                // 任务类型 1
                SingleBitField taskType = new SingleBitField("taskType", Constants.taskTypeConvert);
                taskList.add(taskType);

                // 任务开始时间 7
                DateField taskStartTime = new DateField("taskStartTime");
                taskList.add(taskStartTime);

                // 任务结束时间 7
                DateField taskEndTime = new DateField("taskEndTime");
                taskList.add(taskEndTime);

                // 周期性 1
                UnSignedByteField repeatType = new UnSignedByteField("repeatType");
                taskList.add(repeatType);

                // 预留 4
                FixedLengthField taskReserve = new FixedLengthField("taskReserve", 4);
                taskList.add(taskReserve);

                Map<String, Object> taskMap = BinaryParser.parseFields(byteBuf, taskList);

                byte[] taskReserveBytes = (byte[]) taskMap.get("taskReserve");
                taskMap.put("taskReserve", new String(taskReserveBytes));
                log.info("目标名称：{}, 任务解析结果：{}", tName, taskMap);

                //
                Map<String, Boolean> type = (Map<String, Boolean>) taskMap.get("taskType");
                for (Map.Entry<String, Boolean> entry : type.entrySet()) {
                    if (entry.getValue()) {
                        taskMap.put("taskType", entry.getKey());
                        RequirementTaskDTO taskDTO = BeanUtil.mapToBean(taskMap, RequirementTaskDTO.class,
                                true, CopyOptions.create().ignoreNullValue());

                        // 手动赋值
                        taskDTO.setId(taskMap.get("taskId").toString());
                        taskDTO.setStartTime(DateUtil.parseLocalDateTime(taskMap.get("taskStartTime").toString()));
                        taskDTO.setEndTime(DateUtil.parseLocalDateTime(taskMap.get("taskEndTime").toString()));
                        taskDTO.setTaskType(Arrays.asList(Integer.parseInt(taskMap.get("taskType").toString())));
                        tasks.add(taskDTO);
                    }
                }

            }
            /** 解析任务 end **/

            targetInfo.setTracks(tracks);

            targetInfo.setTasks(tasks);

            targetInfos.add(targetInfo);
        }
        /** 解析需求信息 end **/

        requirementInfoDTO.setTargetInfos(targetInfos);
        log.info("需求信息最终解析结果：{}", requirementInfoDTO);

        return requirementInfoDTO;
    }

}
