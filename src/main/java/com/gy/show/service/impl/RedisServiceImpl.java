package com.gy.show.service.impl;

import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class RedisServiceImpl implements RedisService {

    @Override
    public void deleteKeys(String keyPrefix) {
        // 清除锁定状态缓存
        Cursor<byte[]> cursor = RedisUtil.getRedisTemplate().execute((RedisCallback<Cursor<byte[]>>) connection -> {
            // 构建 SCAN 参数
            ScanOptions scanOptions = ScanOptions.scanOptions()
                    .match(keyPrefix + "*")  // 匹配键模式
                    .count(100)             // 每次扫描数量
                    .build();

            // 使用原生连接执行 SCAN
            return connection.scan(scanOptions);
        });
        try {
            while (cursor.hasNext()) {
                java.lang.String key = new java.lang.String(cursor.next());

                RedisUtil.KeyOps.delete(key);
            }
        }  finally {
            if (cursor != null) {
                try {
                    cursor.close(); // 必须关闭游标释放资源
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }
    }

}
