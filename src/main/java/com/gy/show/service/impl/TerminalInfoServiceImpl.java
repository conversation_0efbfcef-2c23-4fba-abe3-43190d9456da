package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.TerminalInfo;
import com.gy.show.mapper.TerminalInfoMapper;
import com.gy.show.service.TerminalInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TerminalInfoServiceImpl extends ServiceImpl<TerminalInfoMapper, TerminalInfo> implements TerminalInfoService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTerminalStatus(int terminalSeq, int status) {
        TerminalInfo terminalInfo = getOne(Wrappers.<TerminalInfo>lambdaQuery().eq(TerminalInfo::getTerminalSeq, terminalSeq));

        if (terminalInfo != null && terminalInfo.getTerminalStatus() != status) {
            // 将状态设置为已使用
            terminalInfo.setTerminalStatus(status);

            // 更新状态
            updateById(terminalInfo);
        }
    }
}
