package com.gy.show.service;

import com.gy.show.entity.dto.external.SpaceTargetControlDTO;
import io.netty.buffer.ByteBuf;

import java.util.Map;

public interface StationSpaceStatusService {

    void packageMessageHead(ByteBuf byteBuf, int type);

    void packageControlMessage(ByteBuf byteBuf, SpaceTargetControlDTO spaceTargetControlDTO);

    void spaceCommand(SpaceTargetControlDTO spaceTargetControlDTO);

    void statusCommand();

    Map<String, Object> parseMessageHead(ByteBuf byteBuf);

    Map<String, Object> parseControlMessageBody(ByteBuf byteBuf);

    Map<String, Object> parseStatusMessageBody(ByteBuf byteBuf);

    void parseSpaceMessage(ByteBuf byteBuf, String id);
}
