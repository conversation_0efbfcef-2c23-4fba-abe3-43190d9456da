package com.gy.show.service;

import com.gy.show.entity.dto.external.NodeSelectDTO;
import com.gy.show.entity.dto.external.TerminalAntiJammingDTO;
import com.gy.show.entity.dto.external.TerminalDataTransferDTO;
import com.gy.show.entity.dto.external.TerminalMachineDTO;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;

import java.util.Map;

public interface TerminalDataService {

    void parseTerminalMessage(ChannelHandlerContext ctx, ByteBuf byteBuf);

    void sendMachineParams(TerminalMachineDTO terminalMachineDTO);

    void sendAntiJammingParams(TerminalAntiJammingDTO terminalAntiJammingDTO);

    void dataTransfer(TerminalDataTransferDTO dataTransferDTO);

    void executeFinalSwitch(String terminalId);

    void notifyFrontNodeSwitch(long timeLeft, Map<Object, Object> state, String key);

    void confirmSwitch(NodeSelectDTO nodeSelectDTO);

    void cancelSwitch(NodeSelectDTO nodeSelectDTO);

    void switchSpaceStation(String stationId, int isOpen);

    void switchUavStation(int isOpen);

    void switchMissileStation(int isOpen);

    void testSwitchStation();
}
