package com.gy.show.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.DataFile;
import org.springframework.web.multipart.MultipartFile;

public interface FileService extends IService<DataFile> {

    String upload(MultipartFile file, String bzPath);

    String getFilePath(String fileId);

    String getFileRealPath(String fileId);

    String importTrackFile(MultipartFile file);

    Object loadTrackFile(String fileId);

    String getImageByType(Integer type);
}
