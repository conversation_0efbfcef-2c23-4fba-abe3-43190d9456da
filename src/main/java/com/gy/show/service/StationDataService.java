package com.gy.show.service;

import com.gy.show.entity.dto.external.StationDataDTO;
import com.gy.show.socket.message.StationSpaceDataHead;
import io.netty.buffer.ByteBuf;

public interface StationDataService {

    ByteBuf writeHeadMessage(int length, Integer target);

    void writeRemoteControl(ByteBuf byteBuf, byte[] content);

    StationSpaceDataHead parseMessageHead(Object msg);

    void parseMessageBody(Object msg, StationSpaceDataHead head, String id);

    void remoteControlCommand(StationDataDTO stationDataDTO);

    void testCarBz();

    void testCarDp();
}
