package com.gy.show.service;

import com.gy.show.entity.dto.ConfirmScheduleDTO;
import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.enums.ExternalDataTypeEnum;
import com.gy.show.socket.message.HeadMessage;

import java.util.List;

public interface ExternalDataService {

    void handlerBusinessData(Object msg);

    void handlerRequirementData(Object msg, ExternalDataTypeEnum dataTypeEnum);

    HeadMessage parseControlMessageHead(Object msg);

    void handlerTargetData(Object msg, ExternalDataTypeEnum dataTypeEnum);

    void handlerSituationData(Object msg, ExternalDataTypeEnum dataTypeEnum);

    void packageScheduleResult(List<ConfirmScheduleDTO> confirmScheduleDTOs);

    void handlerResponseScheduleResult(Object msg);

    void handlerControlData(Object msg, ExternalDataTypeEnum carData);

    void sendBusinessData(byte[] data, ClientTypeEnum dataTypeEnum);

    void simulatePushBzData();
}
