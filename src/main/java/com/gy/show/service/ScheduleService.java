package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.*;
import com.gy.show.entity.dto.external.RespScheduleDTO;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.MultiLineString;
import org.locationtech.jts.geom.Polygon;

import java.util.List;
import java.util.Map;

public interface ScheduleService {

    void confirmSchedule(List<ConfirmScheduleDTO> confirmScheduleDTO);

    Object invokeAlgorithm(AlgorithmDTO basicAlgorithmDTO, IPage page);

    void buildPolygon(Map<String, List<Map<String, Object>>> equipments);

    Map<String, LineString> buildTrackMap(Map<String, List<RequirementTargetTrack>> tracks);

    Map<String, CoordinateWithTimeDTO[]> buildCoordinateMaps(Map<String, List<RequirementTargetTrack>> tracks, RequirementTask task);

    Object calculateCover(CalculateCoverDTO calculateCoverDTO);

    void startSchedule();

    RequirementTask queryTaskByRelationId(String targetId);

    Polygon getSatellitePolygon(String targetId, Map<String, Object> equip, RequirementTask task);

    double sendResult(ScheduleTaskDTO scheduleTaskDTO);

    void renegotiate(ScheduleTaskDTO scheduleTaskDTO);

    Map<String, LineString> buildTrackMap0(Map<String, CoordinateWithTimeDTO[]> coordinateMaps);

    List<LineString> mergeLineStrings(List<LineString> lineStrings);

    void handlerResponseScheduleResult(List<RespScheduleDTO> resp);
}
