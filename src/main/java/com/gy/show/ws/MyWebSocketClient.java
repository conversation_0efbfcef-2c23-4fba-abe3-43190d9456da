package com.gy.show.ws;

import com.alibaba.fastjson.JSON;
import com.gy.show.entity.dto.SimulateTsData;
import com.gy.show.entity.dto.WsMessageDTO;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.util.GISUtils;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.java_websocket.enums.ReadyState;

import java.awt.geom.Point2D;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

public class MyWebSocketClient extends WebSocketClient {

  public   MyWebSocketClient(String url) throws URISyntaxException {
        super(new URI(url));
    }

    // 建立连接
    @Override
    public void onOpen(ServerHandshake shake) {
        System.out.println(shake.getHttpStatusMessage());
    }

    // 接收消息
    @Override
    public void onMessage(String paramString) {
        System.out.println(paramString);
    }

    // 关闭连接
    @Override
    public void onClose(int paramInt, String paramString, boolean paramBoolean) {
        System.out.println("关闭");
    }

    // 连接异常
    @Override
    public void onError(Exception e) {
        System.out.println("发生错误");
    }

    /**
     * 发送
     *
     * @param args
     * @throws URISyntaxException
     * @throws InterruptedException
     */
    public static void main(String[] args) throws URISyntaxException, InterruptedException {
        MyWebSocketClient client = new MyWebSocketClient("ws://192.168.19.252:8090/wrpt/ws/fullViewTs/1111111111");
        client.connect();
        while (client.getReadyState() != ReadyState.OPEN) {
            System.out.println("连接状态：" + client.getReadyState());
            Thread.sleep(100);
        }
        List<Point2D.Double> keyPoint=new ArrayList<>();
        keyPoint.add(new  Point2D.Double(113.04,33.23) );
        keyPoint.add(new  Point2D.Double(90.11,30.23) );
        keyPoint.add(new  Point2D.Double(103.54,29.23) );
        keyPoint.add(new  Point2D.Double(89.04,35.13) );

        List<Point2D.Double> list = GISUtils.insertPoint(keyPoint,100);
        for (Point2D.Double point : list) {
            WsMessageDTO dto = new WsMessageDTO();
            Map<String, Object> msg = new HashMap<>();
            msg.put("x", point.getX());
            msg.put("y", point.getY());
            msg.put("z", 0);
            msg.put("dataType", 4);
            msg.put("dataTypeId", "8");
            msg.put("dataId", "1802512866138689538");
            dto.setData(Arrays.asList(msg));
            dto.setType(WebSocketTypeEnum.FULL_VIEW_TARGET.getCode());
            client.send(JSON.toJSONString(dto));
            Thread.sleep(1000);
        }
//        client.onMessage("aaa");
//        client.close();
    }



    private static SimulateTsData simulatekeyPoints(String name, int trackPointNum) {
        SimulateTsData simulateTsData = new SimulateTsData();
        double latitude = 30.0;
        double longitude = 110.0;
        List<SimulateTsData.Point> keyPoints = new ArrayList<>();
        for (int i = 0; i < trackPointNum; i++) {
            SimulateTsData.Point keyPoint = simulateTsData.new Point();
            keyPoint.setLatitude((Math.random() * 10 + latitude));
            keyPoint.setLongitude((Math.random() * 10 + longitude));
            keyPoints.add(keyPoint);
        }
        simulateTsData.setName(name);
        simulateTsData.setKeyPoint(keyPoints);
        return simulateTsData;
    }

    private static WsMessageDTO sendSimulateData(SimulateTsData simulateTsData) {
        WsMessageDTO dto = new WsMessageDTO();
        Map<String, Object> msg = new HashMap<>();
        msg.put("simulateTsData", simulateTsData);
        dto.setData(msg);
        dto.setType(WebSocketTypeEnum.FULL_VIEW_TARGET.getCode());
        return dto;
    }

    /**
     * 接收
     * @param args
     * @throws URISyntaxException
     * @throws InterruptedException
     */
//    public static void main(String[] args) throws URISyntaxException, InterruptedException {
//        MyWebSocketClient client = new MyWebSocketClient("ws://192.168.19.252:8090/wrpt/ws/global/1723245530855");
//        client.connect();
//        while (client.getReadyState() != ReadyState.OPEN) {
//            System.out.println("连接状态：" + client.getReadyState());
//            Thread.sleep(100);
//        }
//        client.onMessage();
////        client.close();
//    }
}
