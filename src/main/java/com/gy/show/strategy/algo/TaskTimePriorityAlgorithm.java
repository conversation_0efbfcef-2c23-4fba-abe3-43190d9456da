package com.gy.show.strategy.algo;

import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.InvokeAlgorithmDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 连续测控优先
 */
@Slf4j
@Component
public class TaskTimePriorityAlgorithm extends AbstractAlgorithmTemplate {

    @Override
    protected Map<String, List<Map<String, Object>>> doInvokeSelfAlgorithm(InvokeAlgorithmDTO invokeAlgorithmDTO, Map<String, List<Map<String, Object>>> result) {
        // 按设备可执行的任务类型进行过滤
        Map<String, List<Map<String, Object>>> filterResult = filterTaskType(result);

        markLongestContiguousTimePeriod(filterResult, invokeAlgorithmDTO.getTask());
        return filterResult;
    }

    private void markLongestContiguousTimePeriod(Map<String, List<Map<String, Object>>> areaResult, RequirementTask task) {
        // 1. 获取所有带有 entryTime 和 exitTime 的记录
        List<Map<String, Object>> mapList = areaResult.values()
                .stream()
                .flatMap(List::stream)
                .filter(m -> m.get("entryTime") != null && m.get("exitTime") != null)
                .collect(Collectors.toList());

        // 2. 按 entryTime 和时长排序（时长优先，其次按开始时间排序）
        mapList.sort(Comparator.comparing((Map<String, Object> m) ->
                        Duration.between(parseTime(m.get("entryTime")), parseTime(m.get("exitTime"))).toMinutes()).reversed()
                .thenComparing(m -> parseTime(m.get("entryTime"))));

        // 3. 找到覆盖时长最长的时间段作为调度起点
        Map<String, Object> longestPeriod = mapList.get(0);
        List<Map<String, Object>> result = new ArrayList<>();
        markAsValid(longestPeriod);
        result.add(longestPeriod);

        // 删除这条数据
        mapList.remove(0);

        // 初始化当前覆盖的时间窗口
        final LocalDateTime[] currentStart = {parseTime(longestPeriod.get("entryTime"))};
        final LocalDateTime[] currentEnd = {parseTime(longestPeriod.get("exitTime"))};

        // 4. 向左右扩展覆盖范围，优先选择最长的连续时间段
        expandCoverage(mapList, result, currentStart, currentEnd, task);

        // 5. 将结果存入 areaResult
        areaResult.put(task.getId(), result);
    }

    // 将时间段标记为有效
    private void markAsValid(Map<String, Object> period) {
        period.put("marked", 1);  // 标记为有效
    }

    // 解析时间字段
    private LocalDateTime parseTime(Object time) {
        return LocalDateTime.parse(time.toString(), DateTimeFormatter.ISO_DATE_TIME);
    }

    // 向左右扩展时间窗，优先选择能够最大化覆盖的部分
//    private void expandCoverage(List<Map<String, Object>> mapList, List<Map<String, Object>> result,
//                                LocalDateTime[] currentStart, LocalDateTime[] currentEnd, RequirementTask task) {
//        AtomicBoolean expanded = new AtomicBoolean(true);
//        Set<Map<String, Object>> usedPeriods = new HashSet<>();
//
//        while (expanded.get()) {
//            expanded.set(false);
//
//            // 找到左侧的最佳时间段
//            Optional<Map<String, Object>> leftPeriod = mapList.stream()
//                    .filter(m -> !usedPeriods.contains(m))
//                    .filter(m -> parseTime(m.get("entryTime")).isBefore(currentStart[0]) ||
//                            parseTime(m.get("entryTime")).isEqual(currentStart[0]))
//                    .max(Comparator.comparing(m ->
//                            Duration.between(parseTime(m.get("entryTime")), parseTime(m.get("exitTime"))).toMinutes()));
//
//            // 找到右侧的最佳时间段
//            Optional<Map<String, Object>> rightPeriod = mapList.stream()
//                    .filter(m -> !usedPeriods.contains(m))
//                    .filter(m -> parseTime(m.get("exitTime")).isAfter(currentEnd[0]))
////                            parseTime(m.get("exitTime")).isEqual(currentEnd[0]))
//                    .max(Comparator.comparing(m ->
//                            remainingCoverage(m, currentEnd[0])));  // 选择能延续最长时间的段
//
//            // 处理左侧时间段
//            leftPeriod.ifPresent(period -> {
//                markAsValid(period);
//                result.add(0, period);  // 插入到结果开头
//                currentStart[0] = parseTime(period.get("entryTime"));  // 更新 currentStart
//                usedPeriods.add(period);
//                expanded.set(true);
//            });
//
//            // 处理右侧时间段
//            rightPeriod.ifPresent(period -> {
//                markAsValid(period);
//                result.add(period);  // 添加到结果末尾
//                currentEnd[0] = parseTime(period.get("exitTime"));  // 更新 currentEnd
//                usedPeriods.add(period);
//                expanded.set(true);
//            });
//
//            // 检查是否覆盖了整个任务时间段
//            if (currentStart[0].isBefore(task.getStartTime()) && currentEnd[0].isAfter(task.getEndTime())) {
//                break;  // 完成覆盖，退出循环
//            }
//        }
//    }

    // 向左右扩展时间窗，优先选择能够最大化覆盖的部分
    private void expandCoverage(List<Map<String, Object>> mapList, List<Map<String, Object>> result,
                                LocalDateTime[] currentStart, LocalDateTime[] currentEnd, RequirementTask task) {
        AtomicBoolean expanded = new AtomicBoolean(true);
        Set<Map<String, Object>> usedPeriods = new HashSet<>();

        while (expanded.get()) {
            expanded.set(false);

            // 找到左侧的最佳时间段
            Optional<Map<String, Object>> leftPeriod = mapList.stream()
                    .filter(m -> !usedPeriods.contains(m))
                    .filter(m -> parseTime(m.get("entryTime")).isBefore(currentStart[0]) ||
                            parseTime(m.get("entryTime")).isEqual(currentStart[0]))
                    .max(Comparator.comparing(m ->
                            Duration.between(parseTime(m.get("entryTime")), parseTime(m.get("exitTime"))).toMinutes()));

            // 找到右侧的最佳时间段
            Optional<Map<String, Object>> rightPeriod = mapList.stream()
                    .filter(m -> !usedPeriods.contains(m))
                    .filter(m -> parseTime(m.get("exitTime")).isAfter(currentEnd[0]))
                    .max(Comparator.comparing(m -> remainingCoverage(m, currentEnd[0]))); // 选择能延续最长时间的段

            // 处理左侧时间段
            leftPeriod.ifPresent(period -> {
                LocalDateTime entryTime = parseTime(period.get("entryTime"));
                LocalDateTime exitTime = parseTime(period.get("exitTime"));

                // 检查与 currentStart 的重合并调整时间段
                if (exitTime.isAfter(currentStart[0])) {
                    period.put("exitTime", parseTime(currentStart[0]));
                }

                if (!entryTime.isEqual(exitTime)) { // 只保留非空的时间段
                    markAsValid(period);
                    result.add(0, period); // 插入到结果开头
                    currentStart[0] = parseTime(period.get("entryTime")); // 更新 currentStart
                    usedPeriods.add(period);
                    expanded.set(true);
                }
            });

            // 处理右侧时间段
            rightPeriod.ifPresent(period -> {
                LocalDateTime entryTime = parseTime(period.get("entryTime"));
                LocalDateTime exitTime = parseTime(period.get("exitTime"));

                // 检查与 currentEnd 的重合并调整时间段
                if (entryTime.isBefore(currentEnd[0])) {
                    period.put("entryTime", parseTime(currentEnd[0]));
                }

                if (!entryTime.isEqual(exitTime)) { // 只保留非空的时间段
                    markAsValid(period);
                    result.add(period); // 添加到结果末尾
                    currentEnd[0] = parseTime(period.get("exitTime")); // 更新 currentEnd
                    usedPeriods.add(period);
                    expanded.set(true);
                }
            });

            // 检查是否覆盖了整个任务时间段
            if (currentStart[0].isBefore(task.getStartTime()) && currentEnd[0].isAfter(task.getEndTime())) {
                break; // 完成覆盖，退出循环
            }
        }
    }

    // 计算给定时间段的剩余覆盖时长
    private long remainingCoverage(Map<String, Object> period, LocalDateTime currentEnd) {
        LocalDateTime entryTime = parseTime(period.get("entryTime"));
        LocalDateTime exitTime = parseTime(period.get("exitTime"));

        // 如果时间段在当前结束时间之后，返回它的时长
        if (entryTime.isAfter(currentEnd)) {
            return Duration.between(entryTime, exitTime).toMinutes();
        }
        // 否则返回剩余的覆盖时长
        return Duration.between(currentEnd, exitTime).toMinutes();
    }

}
